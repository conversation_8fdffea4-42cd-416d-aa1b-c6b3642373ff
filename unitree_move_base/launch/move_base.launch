<launch>
  <node pkg="move_base" type="move_base" respawn="false" name="move_base" output="screen" clear_params="true">
    <rosparam file="$(find unitree_move_base)/config/costmap_common_params.yaml" command="load" ns="global_costmap" />
    <rosparam file="$(find unitree_move_base)/config/costmap_common_params.yaml" command="load" ns="local_costmap" />
    <rosparam file="$(find unitree_move_base)/config/local_costmap_params.yaml" command="load" />
    <rosparam file="$(find unitree_move_base)/config/global_costmap_params.yaml" command="load" />
    <rosparam file="$(find unitree_move_base)/config/base_local_planner_params.yaml" command="load" />
  </node>
</launch>