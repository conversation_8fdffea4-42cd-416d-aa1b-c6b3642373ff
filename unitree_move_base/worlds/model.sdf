<?xml version='1.0'?>
<sdf version='1.6'>
  <model name='building'>
    <pose frame=''>-1.48575 -2.96603 0 0 -0 0</pose>
    <link name='Wall_0'>
      <collision name='Wall_0_Collision'>
        <geometry>
          <box>
            <size>2.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_0_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-14.9721 6.52571 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_1'>
      <collision name='Wall_1_Collision'>
        <geometry>
          <box>
            <size>30.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_1_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>30.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>0.077883 7.57571 0 0 -0 0</pose>
    </link>
    <link name='Wall_11'>
      <collision name='Wall_11_Collision'>
        <geometry>
          <box>
            <size>18.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_11_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>18.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>5.82788 -3.02429 0 0 -0 3.14159</pose>
    </link>
    <link name='Wall_14'>
      <collision name='Wall_14_Collision'>
        <geometry>
          <box>
            <size>4.5 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_14_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4.5 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.98743 -5.3597 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_19'>
      <collision name='Wall_19_Collision'>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_19_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>2 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.98743 0.834126 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_2'>
      <collision name='Wall_2_Collision'>
        <geometry>
          <box>
            <size>10.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_2_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>10.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>15.1279 2.27571 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_21'>
      <collision name='Wall_21_Collision'>
        <geometry>
          <box>
            <size>8.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_21_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>8.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-11.0779 1.00143 0 0 -0 3.14159</pose>
    </link>
    <link name='Wall_23'>
      <collision name='Wall_23_Collision'>
        <geometry>
          <box>
            <size>3.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_23_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>3.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.98743 5.55241 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_24'>
      <collision name='Wall_24_Collision'>
        <geometry>
          <box>
            <size>0.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_24_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>0.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.98743 7.40241 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_28'>
      <collision name='Wall_28_Collision'>
        <geometry>
          <box>
            <size>7.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_28_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>7.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>7.50962 0.969723 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_30'>
      <collision name='Wall_30_Collision'>
        <geometry>
          <box>
            <size>0.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_30_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>0.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>7.50962 -2.88028 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_32'>
      <collision name='Wall_32_Collision'>
        <geometry>
          <box>
            <size>1 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_32_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>7.45893 7.19929 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_37'>
      <collision name='Wall_37_Collision'>
        <geometry>
          <box>
            <size>1 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_37_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.54022 -3.0261 0 0 -0 3.14159</pose>
    </link>
    <link name='Wall_4'>
      <collision name='Wall_4_Collision'>
        <geometry>
          <box>
            <size>8.65 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_4_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>8.65 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-14.9721 1.22571 0 0 -0 1.5708</pose>
    </link>
    <link name='Wall_44'>
      <collision name='Wall_44_Collision'>
        <geometry>
          <box>
            <size>1.20526 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_44_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>1.20526 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-6.98173 -2.6571 0 0 -0 -1.5816</pose>
    </link>
    <link name='Wall_7'>
      <collision name='Wall_7_Collision'>
        <geometry>
          <box>
            <size>4.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_7_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>-14.9721 -5.32429 0 0 -0 -1.5708</pose>
    </link>
    <link name='Wall_8'>
      <collision name='Wall_8_Collision'>
        <geometry>
          <box>
            <size>30.25 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_8_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>30.25 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>0.077883 -7.62429 0 0 -0 0</pose>
    </link>
    <link name='Wall_9'>
      <collision name='Wall_9_Collision'>
        <geometry>
          <box>
            <size>4.75 0.15 2.5</size>
          </box>
        </geometry>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
      </collision>
      <visual name='Wall_9_Visual'>
        <pose frame=''>0 0 1.25 0 -0 0</pose>
        <geometry>
          <box>
            <size>4.75 0.15 2.5</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Bricks</name>
          </script>
          <ambient>1 1 1 1</ambient>
        </material>
        <meta>
          <layer>0</layer>
        </meta>
      </visual>
      <pose frame=''>15.1279 -5.32429 0 0 -0 1.5708</pose>
    </link>
    <static>1</static>
  </model>
</sdf>
