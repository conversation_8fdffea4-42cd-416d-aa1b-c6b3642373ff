cmake_minimum_required(VERSION 3.8)
project(him_pkg)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 设置正确的Python路径
if(UNIX AND NOT APPLE)
  set(Python_ROOT_DIR "/opt/miniconda3/envs/himloco")
  set(Python_EXECUTABLE "/opt/miniconda3/envs/himloco/bin/python")
  set(Python_INCLUDE_DIRS "/opt/miniconda3/envs/himloco/include/python3.10")
  set(Python_LIBRARIES "/opt/miniconda3/envs/himloco/lib/libpython3.10.so")
endif()

if(APPLE)
  # 使用环境变量获取正确的conda环境路径
  set(Python_ROOT_DIR "$ENV{CONDA_PREFIX}")
  set(Python_EXECUTABLE "$ENV{CONDA_PREFIX}/bin/python")

  # 添加到CMAKE_PREFIX_PATH确保find_package能找到
  list(APPEND CMAKE_PREFIX_PATH "$ENV{CONDA_PREFIX}")

  # 打印调试信息
  message(STATUS "CONDA_PREFIX: $ENV{CONDA_PREFIX}")
  message(STATUS "Setting Python_ROOT_DIR to: ${Python_ROOT_DIR}")
  message(STATUS "Setting Python_EXECUTABLE to: ${Python_EXECUTABLE}")
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclcpp REQUIRED)
find_package(lowlevel_msg REQUIRED)

# 查找Python
find_package(Python COMPONENTS Interpreter Development REQUIRED)

# 打印Python信息用于调试
message(STATUS "Found Python executable: ${Python_EXECUTABLE}")
message(STATUS "Found Python version: ${Python_VERSION}")
message(STATUS "Found Python include dirs: ${Python_INCLUDE_DIRS}")
message(STATUS "Found Python libraries: ${Python_LIBRARIES}")
message(STATUS "Python module extension: ${PYTHON_MODULE_EXTENSION}")
message(STATUS "Python module prefix: ${PYTHON_MODULE_PREFIX}")

# 尝试查找pybind11
find_package(pybind11 QUIET)

if(NOT pybind11_FOUND)
  find_package(pybind11_vendor REQUIRED)
  find_package(pybind11 REQUIRED)
endif()

# 包含头文件目录
include_directories(include)
include_directories(${Python_INCLUDE_DIRS})

# Python setup - 必须在创建C++目标之前设置
ament_python_install_package(${PROJECT_NAME})

# 创建C++低级状态订阅者库
add_library(lowstate_subscriber_lib SHARED
  src/lowstate_subscriber.cpp
)

# 创建C++低级命令发布者库
add_library(lowcmd_publisher_lib SHARED
  src/lowcmd_publisher.cpp
)

# 找到所有必要的类型支持组件
find_package(rosidl_typesupport_fastrtps_cpp REQUIRED)

ament_target_dependencies(lowstate_subscriber_lib
  rclcpp
  lowlevel_msg
  rosidl_typesupport_fastrtps_cpp
)

ament_target_dependencies(lowcmd_publisher_lib
  rclcpp
  lowlevel_msg
  rosidl_typesupport_fastrtps_cpp
)

# 显式链接FastRTPS类型支持库
target_link_libraries(lowstate_subscriber_lib
  ${lowlevel_msg_TARGETS}
)

target_link_libraries(lowcmd_publisher_lib
  ${lowlevel_msg_TARGETS}
)

# 创建pybind11模块 - 使用更兼容的方法
if(pybind11_FOUND)
  # 使用传统的add_library方法
  add_library(lowstate_subscriber_py MODULE src/pybind_lowstate.cpp)
  add_library(lowcmd_publisher_py MODULE src/pybind_lowcmd.cpp)

  # 设置包含目录
  target_include_directories(lowstate_subscriber_py PRIVATE ${Python_INCLUDE_DIRS})
  target_include_directories(lowcmd_publisher_py PRIVATE ${Python_INCLUDE_DIRS})

  # 先设置ament依赖
  ament_target_dependencies(lowstate_subscriber_py
    rclcpp
    lowlevel_msg
    rosidl_typesupport_fastrtps_cpp
  )

  ament_target_dependencies(lowcmd_publisher_py
    rclcpp
    lowlevel_msg
    rosidl_typesupport_fastrtps_cpp
  )

  # 然后链接其他库
  target_link_libraries(lowstate_subscriber_py
    lowstate_subscriber_lib
    pybind11::module
  )

  target_link_libraries(lowcmd_publisher_py
    lowcmd_publisher_lib
    pybind11::module
  )

  # 设置pybind11模块属性
  set_target_properties(lowstate_subscriber_py PROPERTIES
    PREFIX "${PYTHON_MODULE_PREFIX}"
    SUFFIX "${PYTHON_MODULE_EXTENSION}"
  )

  set_target_properties(lowcmd_publisher_py PROPERTIES
    PREFIX "${PYTHON_MODULE_PREFIX}"
    SUFFIX "${PYTHON_MODULE_EXTENSION}"
  )

  # 在Linux上设置正确的rpath
  if(UNIX AND NOT APPLE)
    set_target_properties(lowstate_subscriber_lib PROPERTIES
      INSTALL_RPATH "$ORIGIN"
    )
    set_target_properties(lowcmd_publisher_lib PROPERTIES
      INSTALL_RPATH "$ORIGIN"
    )
    set_target_properties(lowstate_subscriber_py PROPERTIES
      INSTALL_RPATH "$ORIGIN:$ORIGIN/../../lib"
      BUILD_WITH_INSTALL_RPATH TRUE
    )
    set_target_properties(lowcmd_publisher_py PROPERTIES
      INSTALL_RPATH "$ORIGIN:$ORIGIN/../../lib"
      BUILD_WITH_INSTALL_RPATH TRUE
    )
  endif()

  # 在macOS上设置正确的rpath
  if(APPLE)
    # 清除默认的rpath行为，避免重复
    set_target_properties(lowstate_subscriber_lib PROPERTIES
      INSTALL_RPATH_USE_LINK_PATH FALSE
      BUILD_WITH_INSTALL_RPATH TRUE
      INSTALL_RPATH "@loader_path/../../../../../lowlevel_msg/lib"
    )
    set_target_properties(lowcmd_publisher_lib PROPERTIES
      INSTALL_RPATH_USE_LINK_PATH FALSE
      BUILD_WITH_INSTALL_RPATH TRUE
      INSTALL_RPATH "@loader_path/../../../../../lowlevel_msg/lib"
    )
    set_target_properties(lowstate_subscriber_py PROPERTIES
      INSTALL_RPATH_USE_LINK_PATH FALSE
      BUILD_WITH_INSTALL_RPATH TRUE
      INSTALL_RPATH "@loader_path;@loader_path/../../../../lib;@loader_path/../../../../../lowlevel_msg/lib"
    )
    set_target_properties(lowcmd_publisher_py PROPERTIES
      INSTALL_RPATH_USE_LINK_PATH FALSE
      BUILD_WITH_INSTALL_RPATH TRUE
      INSTALL_RPATH "@loader_path;@loader_path/../../../../lib;@loader_path/../../../../../lowlevel_msg/lib"
    )
  endif()

  # 安装pybind11模块
  install(TARGETS lowstate_subscriber_py lowcmd_publisher_py
    DESTINATION "${PYTHON_INSTALL_DIR}/${PROJECT_NAME}"
  )

  # 将C++库也安装到Python包目录中
  install(TARGETS lowstate_subscriber_lib lowcmd_publisher_lib
    DESTINATION "${PYTHON_INSTALL_DIR}/${PROJECT_NAME}"
  )

  # 安装必需的lowlevel_msg类型支持库到Python包目录
  # 在macOS上，安装后复制依赖的类型支持库
  if(APPLE)
    install(CODE "
      file(GLOB LOWLEVEL_LIBS \"\${CMAKE_INSTALL_PREFIX}/lowlevel_msg/lib/liblowlevel_msg__rosidl_typesupport_*.dylib\")
      foreach(lib \${LOWLEVEL_LIBS})
        get_filename_component(lib_name \${lib} NAME)
        execute_process(COMMAND \${CMAKE_COMMAND} -E copy \${lib} \"\${CMAKE_INSTALL_PREFIX}/${PYTHON_INSTALL_DIR}/${PROJECT_NAME}/\${lib_name}\")
        message(STATUS \"Copied \${lib} to Python package directory\")
      endforeach()
    ")
  endif()

  message(STATUS "pybind11 module will be built with Python: ${Python_EXECUTABLE}")
  message(STATUS "Python include dirs: ${Python_INCLUDE_DIRS}")
  message(STATUS "Python libraries: ${Python_LIBRARIES}")
else()
  message(WARNING "pybind11 not found, skipping C++ module build")
endif()

# 安装C++库到标准位置
install(TARGETS lowstate_subscriber_lib lowcmd_publisher_lib
  DESTINATION lib
)

install(PROGRAMS
  ${PROJECT_NAME}/deploy_policy_ros_multithread.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME him_pkg_mt
)

# 安装数据文件到Python包目录
install(FILES
  ${PROJECT_NAME}/libmodel_task.so
  DESTINATION "${PYTHON_INSTALL_DIR}/${PROJECT_NAME}"
)

# 安装runs目录到share目录（ROS2包期望的位置）
install(DIRECTORY
  ${PROJECT_NAME}/runs/
  DESTINATION share/${PROJECT_NAME}/runs
)

# 同时也安装到Python包目录（以防万一）
install(DIRECTORY
  ${PROJECT_NAME}/runs/
  DESTINATION "${PYTHON_INSTALL_DIR}/${PROJECT_NAME}/runs"
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
