#pragma once

#include <rclcpp/rclcpp.hpp>
#include <lowlevel_msg/msg/low_cmd.hpp>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <array>

namespace him_pkg {

// 简化的 LowCmd 数据结构，用于 Python 传递数据
struct SimpleLowCmd {
    // 电机命令数据 (12个电机)
    std::array<float, 12> motor_q;
    std::array<float, 12> motor_dq;
    std::array<float, 12> motor_kp;
    std::array<float, 12> motor_kd;
    std::array<float, 12> motor_tau;
    
    // 控制ID和时间戳
    uint16_t cmd_id;
    uint16_t input_state_id;
    uint64_t ts_infer_input_state;
    uint64_t ts_before_infer;
    uint64_t ts_after_infer;
    
    SimpleLowCmd() {
        motor_q.fill(0.0f);
        motor_dq.fill(0.0f);
        motor_kp.fill(0.0f);
        motor_kd.fill(0.0f);
        motor_tau.fill(0.0f);
        cmd_id = 0;
        input_state_id = 0;
        ts_infer_input_state = 0;
        ts_before_infer = 0;
        ts_after_infer = 0;
    }
};

class LowCmdPublisher {
public:
    LowCmdPublisher(const std::string& topic_name = "/rt/lowcmd");
    ~LowCmdPublisher();
    
    // 启动发布者
    void start();
    
    // 停止发布者
    void stop();
    
    // 检查是否运行中
    bool is_running() const;
    
    // 发布消息（主要接口）
    void publish_message(const SimpleLowCmd& cmd_data);

private:
    // 将简化数据转换为完整的ROS2消息
    lowlevel_msg::msg::LowCmd::SharedPtr convert_to_ros_message(const SimpleLowCmd& cmd_data);
    
    rclcpp::Node::SharedPtr node_;
    rclcpp::Publisher<lowlevel_msg::msg::LowCmd>::SharedPtr publisher_;
    std::string topic_name_;
    std::atomic<bool> running_;
    
    // 线程安全
    mutable std::mutex publish_mutex_;
    
    // 用于ROS2 spin的执行器
    rclcpp::executors::SingleThreadedExecutor::SharedPtr executor_;
    std::thread spin_thread_;
};

} // namespace him_pkg 