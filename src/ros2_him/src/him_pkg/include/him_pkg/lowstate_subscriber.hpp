#pragma once

#include <rclcpp/rclcpp.hpp>
#include <lowlevel_msg/msg/low_state.hpp>
#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>

namespace him_pkg {

class LowStateSubscriber {
public:
    using CallbackType = std::function<void(const lowlevel_msg::msg::LowState::SharedPtr)>;
    
    LowStateSubscriber(const std::string& topic_name = "/rt/lowstate");
    ~LowStateSubscriber();
    
    // 设置Python回调函数
    void set_callback(CallbackType callback);
    
    // 启动订阅
    void start();
    
    // 停止订阅
    void stop();
    
    // 检查是否运行中
    bool is_running() const;

private:
    void lowstate_callback(const lowlevel_msg::msg::LowState::SharedPtr msg);
    
    rclcpp::Node::SharedPtr node_;
    rclcpp::Subscription<lowlevel_msg::msg::LowState>::SharedPtr subscription_;
    CallbackType python_callback_;
    std::string topic_name_;
    std::atomic<bool> running_;
    
    // 线程安全
    mutable std::mutex callback_mutex_;
    
    // 用于ROS2 spin的执行器
    rclcpp::executors::SingleThreadedExecutor::SharedPtr executor_;
    std::thread spin_thread_;
};

} // namespace him_pkg 