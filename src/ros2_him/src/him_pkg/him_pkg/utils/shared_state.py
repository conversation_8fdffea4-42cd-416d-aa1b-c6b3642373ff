import numpy as np
import threading
import time
from lowlevel_msg.msg import LowState
from sensor_msgs.msg import Joy
from geometry_msgs.msg import Twist

class SharedState:
    """
    共享状态类，用于在不同节点间传递数据
    参考test_sub.py的思路，使用全局共享变量和锁来保证线程安全
    """
    def __init__(self):
        # 用于同步的锁
        self.lock = threading.Lock()
        
        # 系统激活标志
        self.is_activated = False
        self.activation_button_1_pressed = False # LB
        self.activation_button_2_pressed = False # A
        
        # 机器人关节信息
        self.joint_pos = np.zeros(12)
        self.joint_vel = np.zeros(12)
        self.tau_est = np.zeros(12)
        
        # 机器人位置和姿态信息
        self.world_lin_vel = np.zeros(3)
        self.world_ang_vel = np.zeros(3)
        self.euler = np.zeros(3)
        
        # IMU和角速度计算相关
        self.buf_idx = 0
        self.smoothing_length = 12
        self.deuler_history = np.zeros((self.smoothing_length, 3))
        self.dt_history = np.ones((self.smoothing_length, 1))
        self.euler_prev = np.zeros(3)
        self.timuprev = time.time()
        
        # 身体速度
        self.body_lin_vel = np.zeros(3)
        self.body_ang_vel = np.zeros(3)
        self.smoothing_ratio = 0.2
        
        # 接触状态
        self.contact_state = np.ones(4)
        
        # 手柄控制相关
        self.left_upper_switch = 0
        self.left_lower_switch = 0
        self.right_upper_switch = 0
        self.right_lower_switch = 0
        self.left_upper_switch_pressed = 0
        self.left_lower_switch_pressed = 0
        self.right_upper_switch_pressed = 0
        self.right_lower_switch_pressed = 0
        
        # 状态标识符
        self.ts_infer_input_state = 0
        self.input_state_id = 0
        self.input_state_rela_cmd_id = 0
        
        # 接收消息的时间戳
        self.init_time = time.time()
        self.received_first_legdata = False
        self.joy_cmd_receive_time = 0
        self.follow_cmd_receive_time = 0
        
        # 手柄控制命令（已映射为物理量）
        self.joy_cmd_x = 0.0
        self.joy_cmd_y = 0.0
        self.joy_cmd_yaw = 0.0
        
        # 外部控制命令
        self.follow_cmd_x = 0.0
        self.follow_cmd_y = 0.0
        self.follow_cmd_yaw = 0.0
        
        # 腿部和接触点索引
        self.joint_idxs = [3, 4, 5, 0, 1, 2, 9, 10, 11, 6, 7, 8]
        self.contact_idxs = [1, 0, 3, 2] 