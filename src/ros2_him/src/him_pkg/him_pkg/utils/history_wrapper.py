# import isaacgym

# assert isaacgym, "import isaacgym before pytorch"
import torch


class HistoryWrapper:
    def __init__(self, env):
        self.env = env

        # 检查设备可用性，没有CUDA时回退到CPU
        if hasattr(self.env, 'device'):
            self.device = self.env.device
        else:
            # 安全检查CUDA是否真的可用
            cuda_available = False
            try:
                cuda_available = torch.cuda.is_available()
                if cuda_available:
                    # 尝试一个简单的CUDA操作来验证PyTorch是否支持CUDA
                    test_tensor = torch.zeros(1, device="cuda")
            except (AssertionError, RuntimeError):
                cuda_available = False
            
            self.device = "cuda" if cuda_available else "cpu"
            
        if isinstance(self.env.cfg, dict):
            self.obs_history_length = int(self.env.cfg["env"]["num_observations"] / self.env.cfg["env"]["num_one_step_observations"])
        else:
            self.obs_history_length = self.env.cfg.env.num_observation_history
        self.num_obs_history = self.obs_history_length * self.env.num_obs
        
        # 安全创建张量，确保始终使用正确的设备
        try:
            self.obs_history = torch.zeros(self.env.num_envs, self.num_obs_history, dtype=torch.float,
                                       device=self.device, requires_grad=False)
        except Exception as e:
            print(f"无法在设备 {self.device} 上创建张量: {e}")
            print("回退到CPU")
            self.device = "cpu"
            self.obs_history = torch.zeros(self.env.num_envs, self.num_obs_history, dtype=torch.float,
                                       device=self.device, requires_grad=False)
        
        self.num_privileged_obs = self.env.num_privileged_obs

    def step(self, action):
        obs, rew, done, info = self.env.step(action)
        privileged_obs = info["privileged_obs"]

        # 确保张量在正确的设备上
        if obs.device != self.device:
            obs = obs.to(self.device)
        
        self.obs_history = torch.cat((obs, self.obs_history[:, :-self.env.num_obs]), dim=-1)
        return {'obs': obs, 'privileged_obs': privileged_obs, 'obs_history': self.obs_history}, rew, done, info

    # def get_observations(self):
    #     self.obs_history = torch.cat((self.obs_history[:, self.env.num_obs:], self.obs_buf), dim=-1)
    #     obs = self.env.get_observations()
    #     privileged_obs = self.env.get_privileged_observations()
    #     # self.obs_history = torch.cat((self.obs_history[:, self.env.num_obs:], obs), dim=-1)
    #     self.obs_buf = obs
    #     return {'obs': obs, 'privileged_obs': privileged_obs, 'obs_history': self.obs_history}

    def get_obs(self):
        obs = self.env.get_obs()
        privileged_obs = self.env.get_privileged_observations()
        
        # 确保张量在正确的设备上
        if obs.device != self.device:
            obs = obs.to(self.device)
            
        self.obs_history = torch.cat((obs, self.obs_history[:, :-self.env.num_obs]), dim=-1)
        return {'obs': obs, 'privileged_obs': privileged_obs, 'obs_history': self.obs_history}

    def reset_idx(self, env_ids):  # it might be a problem that this isn't getting called!!
        ret = self.env.reset_idx(env_ids)
        self.obs_history[env_ids, :] = 0
        return ret

    def reset(self):
        ret = self.env.reset()
        privileged_obs = self.env.get_privileged_observations()
        self.obs_history[:, :] = 0
        return {"obs": ret, "privileged_obs": privileged_obs, "obs_history": self.obs_history}

    def __getattr__(self, name):
        return getattr(self.env, name)
