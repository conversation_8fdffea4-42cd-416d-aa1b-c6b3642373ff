import math
import numpy as np
import time
import logging

from scipy.spatial.transform import Rotation as R
from him_pkg.utils.shared_state import SharedState

def get_rotation_matrix_from_rpy(rpy):
    """
    从roll-pitch-yaw获取旋转矩阵
    """
    r, p, y = rpy
    R_x = np.array([[1, 0, 0],
                    [0, math.cos(r), -math.sin(r)],
                    [0, math.sin(r), math.cos(r)]
                    ])

    R_y = np.array([[math.cos(p), 0, math.sin(p)],
                    [0, 1, 0],
                    [-math.sin(p), 0, math.cos(p)]
                    ])

    R_z = np.array([[math.cos(y), -math.sin(y), 0],
                    [math.sin(y), math.cos(y), 0],
                    [0, 0, 1]
                    ])

    rot = np.dot(R_z, np.dot(R_y, R_x))
    return rot


class StateEstimator:
    """
    状态估计器类，从共享状态中读取数据
    """
    def __init__(self, shared_state):
        self.shared_state = shared_state
        self.joint_idxs = self.shared_state.joint_idxs
        self.contact_idxs = self.shared_state.contact_idxs
        self._is_cleaned_up = False
        self._command_timeout_flag = False  # 添加超时标志
        self._follow_cmd_timeout_flag = False  # 添加follow_cmd超时标志

    def get_body_linear_vel(self):
        with self.shared_state.lock:
            R = get_rotation_matrix_from_rpy(self.shared_state.euler)
            body_lin_vel = np.dot(R.T, self.shared_state.world_lin_vel)
            self.shared_state.body_lin_vel = body_lin_vel
            return body_lin_vel

    def get_body_angular_vel(self):
        with self.shared_state.lock:
            self.shared_state.body_ang_vel = (
                self.shared_state.smoothing_ratio * 
                np.mean(self.shared_state.deuler_history / self.shared_state.dt_history, axis=0) + 
                (1 - self.shared_state.smoothing_ratio) * self.shared_state.body_ang_vel
            )
            return self.shared_state.body_ang_vel

    def get_gravity_vector(self):
        with self.shared_state.lock:
            R = get_rotation_matrix_from_rpy(self.shared_state.euler)
            grav = np.dot(R.T, np.array([0, 0, -1]))
            return grav

    def get_contact_state(self):
        with self.shared_state.lock:
            return self.shared_state.contact_state[self.contact_idxs]

    def get_rpy(self):
        with self.shared_state.lock:
            return self.shared_state.euler

    def get_command(self):
        with self.shared_state.lock:
            # always in use
            cmd_x = self.shared_state.joy_cmd_x
            # cmd_x = 0.5
            cmd_y = self.shared_state.joy_cmd_y
            cmd_yaw = self.shared_state.joy_cmd_yaw

            # 检查follow_cmd是否超时
            current_time = time.monotonic()
            follow_cmd_timeout = (
                self.shared_state.follow_cmd_receive_time != 0
                and current_time - self.shared_state.follow_cmd_receive_time > 1
            )

            # 如果follow_cmd超时，则将follow_cmd值清零
            if follow_cmd_timeout:
                if not self._follow_cmd_timeout_flag:
                    logging.info("Follow cmd timeout detected, clearing follow_cmd values")
                    self._follow_cmd_timeout_flag = True
                follow_cmd_x = 0.0
                follow_cmd_y = 0.0
                follow_cmd_yaw = 0.0
            else:
                if self._follow_cmd_timeout_flag:
                    logging.info("New follow cmd received, resuming normal follow_cmd operation")
                    self._follow_cmd_timeout_flag = False
                follow_cmd_x = self.shared_state.follow_cmd_x
                follow_cmd_y = self.shared_state.follow_cmd_y
                follow_cmd_yaw = self.shared_state.follow_cmd_yaw

            # 如果手柄输入很小，则使用follow_cmd中的命令（可能已被清零）
            if abs(cmd_x) < 0.1 and abs(cmd_y) < 0.1 and abs(cmd_yaw) < 0.1:
                cmd_x = follow_cmd_x
                cmd_y = follow_cmd_y
                cmd_yaw = follow_cmd_yaw

            # 检查命令超时情况
            joy_cmd_timeout = (
                self.shared_state.joy_cmd_receive_time != 0
                and current_time - self.shared_state.joy_cmd_receive_time > 1
            )

            # 只有当joy_cmd超时且follow_cmd也超时时，才完全停止机器人
            if joy_cmd_timeout and follow_cmd_timeout:
                # 两种命令都超时，命令置零
                cmd_x = 0.
                cmd_y = 0.
                cmd_yaw = 0.
                if not self._command_timeout_flag:
                    logging.info("All commands timeout detected, stopping robot")
                    self._command_timeout_flag = True
            else:
                # 至少有一种命令可用
                if joy_cmd_timeout and not follow_cmd_timeout:
                    # 手柄命令超时但外部命令有效，使用外部命令
                    cmd_x = follow_cmd_x
                    cmd_y = follow_cmd_y
                    cmd_yaw = follow_cmd_yaw
                    if self._command_timeout_flag:
                        logging.info("Using external commands due to joystick timeout")
                        self._command_timeout_flag = False
                elif self._command_timeout_flag:
                    # 手柄命令有效
                    logging.info("Command received, resuming normal operation")
                    self._command_timeout_flag = False

            # 只保留三个必要的命令变量：cmd_x, cmd_y, cmd_yaw
            se_command = np.array([
                cmd_x,
                cmd_y,
                cmd_yaw
            ])

            se_button = np.array([
                self.shared_state.left_lower_switch, 
                self.shared_state.left_upper_switch, 
                self.shared_state.right_lower_switch, 
                self.shared_state.right_upper_switch
            ])

            return se_command, se_button

    def get_buttons(self):
        with self.shared_state.lock:
            return np.array([
                self.shared_state.left_lower_switch, 
                self.shared_state.left_upper_switch, 
                self.shared_state.right_lower_switch, 
                self.shared_state.right_upper_switch
            ])

    def get_dof_pos(self):
        with self.shared_state.lock:
            return np.array(self.shared_state.joint_pos)[self.joint_idxs]

    def get_dof_vel(self):
        with self.shared_state.lock:
            return np.array(self.shared_state.joint_vel)[self.joint_idxs]

    def get_tau_est(self):
        with self.shared_state.lock:
            return np.array(self.shared_state.tau_est)[self.joint_idxs]

    def get_yaw(self):
        with self.shared_state.lock:
            return self.shared_state.euler[2]

    def get_input_state_id(self):
        with self.shared_state.lock:
            return self.shared_state.input_state_id

    def get_ts_infer_input_state(self):
        with self.shared_state.lock:
            return self.shared_state.ts_infer_input_state

    def get_wtw_obs(self):
        """获取完整的状态观测"""
        with self.shared_state.lock:
            R = get_rotation_matrix_from_rpy(self.shared_state.euler)
            body_lin_vel = np.dot(R.T, self.shared_state.world_lin_vel)
            self.shared_state.body_lin_vel = body_lin_vel

            self.shared_state.body_ang_vel = (
                self.shared_state.smoothing_ratio * 
                np.mean(self.shared_state.deuler_history / self.shared_state.dt_history, axis=0) + 
                (1 - self.shared_state.smoothing_ratio) * self.shared_state.body_ang_vel
            )

            return {
                'gravity_vector': np.dot(R.T, np.array([0, 0, -1])),
                'dof_pos': np.array(self.shared_state.joint_pos)[self.joint_idxs],
                'tau_est': np.array(self.shared_state.tau_est)[self.joint_idxs],
                'dof_vel': np.array(self.shared_state.joint_vel)[self.joint_idxs],
                'body_linear_vel': self.shared_state.body_lin_vel,
                'body_angular_vel': self.shared_state.body_ang_vel,
                'input_state_id': self.shared_state.input_state_id,
                'input_state_rela_cmd_id': self.shared_state.input_state_rela_cmd_id,
                'ts_infer_input_state': self.shared_state.ts_infer_input_state,
            }

    def cleanup(self):
        """清理资源"""
        if self._is_cleaned_up:
            return
        self.shared_state = None
        self._is_cleaned_up = True

    def __del__(self):
        self.cleanup() 