"""
C++ LowCmd Publisher Node
借鉴 cpp_lowstate_node 的思路，用 C++ 实现 action publisher 来解决 Python 序列化/反序列化慢的问题
"""

import numpy as np
import time
import atexit
import weakref

# 全局变量
_cpp_module = None
_cpp_loaded = False
_active_publishers = weakref.WeakSet()  # 使用弱引用跟踪活跃的发布者

def load_cpp_module():
    """加载C++模块"""
    global _cpp_module, _cpp_loaded
    
    if _cpp_loaded:
        return _cpp_module is not None
    
    try:
        # 尝试导入C++实现的发布者
        from him_pkg.lowcmd_publisher_py import LowCmdPublisher, SimpleLowCmd, create_simple_lowcmd
        _cpp_module = {
            'LowCmdPublisher': LowCmdPublisher,
            'SimpleLowCmd': SimpleLowCmd,
            'create_simple_lowcmd': create_simple_lowcmd
        }
        _cpp_loaded = True
        print("C++ lowcmd publisher module loaded successfully")
        
        # 注册清理函数
        atexit.register(_cleanup_all_publishers)
        
        return True
    except ImportError as e:
        print(f"Warning: C++ lowcmd publisher not available: {e}")
        _cpp_module = None
        _cpp_loaded = True
        return False

def _cleanup_all_publishers():
    """清理所有活跃的发布者"""
    try:
        # 停止所有活跃的发布者
        publishers_to_cleanup = list(_active_publishers)
        for publisher in publishers_to_cleanup:
            try:
                if hasattr(publisher, '_cleanup'):
                    publisher._cleanup()
            except Exception as e:
                print(f"Error cleaning up publisher: {e}")
        
        # 清理全局模块
        global _cpp_module
        _cpp_module = None
        
    except Exception as e:
        print(f"Error in cleanup_all_publishers: {e}")

class CppLowCmdPublisher:
    """C++ LowCmd Publisher 的 Python 包装类"""
    
    def __init__(self, topic_name="/rt/lowcmd"):
        self.topic_name = topic_name
        self.publisher = None
        self.is_cpp_available = load_cpp_module()
        self._is_started = False
        self._is_cleaned_up = False
        
        if self.is_cpp_available:
            try:
                self.publisher = _cpp_module['LowCmdPublisher'](topic_name)
                # 将此发布者添加到活跃列表
                _active_publishers.add(self)
            except Exception as e:
                print(f"Error creating C++ publisher: {e}")
                raise
        else:
            raise RuntimeError("C++ lowcmd publisher module is not available")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self._cleanup()
    
    def _cleanup(self):
        """内部清理函数"""
        if self._is_cleaned_up:
            return
            
        try:
            self.stop()
            if self.publisher:
                # 释放C++对象
                self.publisher = None
        except Exception as e:
            print(f"Error in CppLowCmdPublisher cleanup: {e}")
        finally:
            self._is_cleaned_up = True
    
    def start(self):
        """启动发布者"""
        if self._is_cleaned_up:
            raise RuntimeError("Publisher has been cleaned up")
            
        if self.publisher and not self._is_started:
            try:
                self.publisher.start()
                self._is_started = True
            except Exception as e:
                print(f"Error starting publisher: {e}")
                raise
    
    def stop(self):
        """停止发布者"""
        if self.publisher and self._is_started:
            try:
                self.publisher.stop()
                self._is_started = False
            except Exception as e:
                print(f"Error stopping publisher: {e}")
    
    def is_running(self):
        """检查是否运行中"""
        if self._is_cleaned_up or not self.publisher:
            return False
        try:
            return self.publisher.is_running()
        except Exception:
            return False
    
    def publish_action(self, joint_pos_target, joint_vel_target, p_gains, d_gains, 
                      cmd_id, input_state_id, ts_infer_input_state, 
                      ts_before_infer, ts_after_infer):
        """
        发布动作命令
        
        Args:
            joint_pos_target: 关节位置目标 (12,)
            joint_vel_target: 关节速度目标 (12,)
            p_gains: P增益 (12,)
            d_gains: D增益 (12,)
            cmd_id: 命令ID
            input_state_id: 输入状态ID
            ts_infer_input_state: 推理输入状态时间戳
            ts_before_infer: 推理前时间戳
            ts_after_infer: 推理后时间戳
        """
        if self._is_cleaned_up:
            raise RuntimeError("Publisher has been cleaned up")
            
        if not self.publisher or not self.is_running():
            raise RuntimeError("Publisher is not running!")
        
        try:
            # 确保输入是numpy数组且为float32类型
            joint_pos_target = np.asarray(joint_pos_target, dtype=np.float32)
            joint_vel_target = np.asarray(joint_vel_target, dtype=np.float32)
            p_gains = np.asarray(p_gains, dtype=np.float32)
            d_gains = np.asarray(d_gains, dtype=np.float32)
            
            # 检查数组大小
            if (joint_pos_target.shape != (12,) or joint_vel_target.shape != (12,) or
                p_gains.shape != (12,) or d_gains.shape != (12,)):
                raise ValueError("All input arrays must have shape (12,)")
            
            # 计算扭矩（这里设为0，因为使用位置控制）
            motor_tau = np.zeros(12, dtype=np.float32)
            
            # 使用便利函数创建SimpleLowCmd
            cmd_data = _cpp_module['create_simple_lowcmd'](
                joint_pos_target,
                joint_vel_target,
                p_gains,
                d_gains,
                motor_tau,
                int(cmd_id),
                int(input_state_id),
                int(ts_infer_input_state),
                int(ts_before_infer),
                int(ts_after_infer)
            )
            
            # 发布消息
            self.publisher.publish_message(cmd_data)
            
        except Exception as e:
            print(f"Error publishing action: {e}")
            raise

def is_cpp_available():
    """检查C++模块是否可用"""
    return load_cpp_module()

def create_cpp_publisher(topic_name="/rt/lowcmd"):
    """创建C++ publisher实例"""
    if not is_cpp_available():
        return None
    return CppLowCmdPublisher(topic_name)

def cleanup_all():
    """手动清理所有资源（可选调用）"""
    _cleanup_all_publishers() 