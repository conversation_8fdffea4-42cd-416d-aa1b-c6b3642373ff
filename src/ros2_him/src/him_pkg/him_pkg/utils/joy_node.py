###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: joy_node.py
# Author : siyu.zhou
# Date : Jun, 2025
# Describe: This script implements joystick control and velocity command handling.
###################################################################

import time

from geometry_msgs.msg import Twist
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup
from rclpy.node import Node
from sensor_msgs.msg import Joy

from std_msgs.msg import Bool # 导入 Bool 消息类型

class JoyNode(Node):
    """
    专门处理手柄(Joy)消息和速度命令的节点
    """
    def __init__(self, shared_state, joy_topic="/joy", vel_cmd_topic="/vel_cmd"):
        super().__init__("joy_node")
        
        self.shared_state = shared_state
        self.joy_topic = joy_topic
        self.vel_cmd_topic = vel_cmd_topic
        
        # 创建回调组，保证所有回调在同一线程中执行
        self.callback_group = MutuallyExclusiveCallbackGroup()
        
        # 摇杆到物理量的映射系数
        self.joy_x_scale = 0.8 # m/s
        self.joy_y_scale = 0.0 # m/s
        self.joy_yaw_scale = 1.0 # rad/s
        
        # 订阅手柄话题
        self.joy_sub = self.create_subscription(
            Joy,
            self.joy_topic,
            self.joy_callback,
            3,
            callback_group=self.callback_group
        )
        
        # 订阅速度命令话题
        self.cmd_sub = self.create_subscription(
            Twist,
            self.vel_cmd_topic,
            self.follow_cmd_callback,
            3,
            callback_group=self.callback_group
        )
        
        self.get_logger().info(f"JoyNode 已启动，监听Joy话题: {self.joy_topic}")
        self.get_logger().info(f"JoyNode 已启动，监听VelCmd话题: {self.vel_cmd_topic}")
        
        # 添加发布者
        self.stairs_publisher = self.create_publisher(
            Bool, 
            'stairsmodel_switch_trigger', 
            10
        )
        self.flat_publisher = self.create_publisher(
            Bool, 
            'flatmodel_switch_trigger', 
            10
        )

        # 修改发布topic
        self.cmd_publisher = self.create_publisher(
            Twist,
            '/joy_cmd',  # 改为发布到joy_cmd
            10
        )


    def joy_callback(self, joy_msg):
        """处理手柄输入回调"""
        # 在锁外解析joy_msg数据
        A = joy_msg.buttons[0]
        B = joy_msg.buttons[1]
        X = joy_msg.buttons[2]
        Y = joy_msg.buttons[3]
        LB = joy_msg.buttons[4]  # 左上肩键
        RB = joy_msg.buttons[5]  # 右上肩键
        LT = 1 if joy_msg.axes[2] < -0.8 else 0  # 左下肩键
        RT = 1 if joy_msg.axes[5] < -0.8 else 0  # 右下肩键
        lx = joy_msg.axes[0]
        ly = joy_msg.axes[1]
        rx = joy_msg.axes[3]
        ry = joy_msg.axes[4]

        with self.shared_state.lock:
            # 立即更新手柄命令接收时间
            current_time = time.monotonic()
            
            if not self.shared_state.is_activated:
                # 系统未激活，检查激活指令 (LB+A)
                if LB > 0 and A > 0:
                    self.shared_state.is_activated = True
                    self.get_logger().info("System ACTIVATED by LB+A combination!")
                    # 激活后，继续处理本次回调中的其他按键
                else:
                    return # 未激活，不处理任何其他输入

            # 检查去激活指令 (LT+A)
            if LT > 0 and A > 0:
                self.get_logger().info("Deactivation command LT+A received. System returning to PENDING state.")
                self.shared_state.is_activated = False
                return  # 去激活，立即停止处理
                
            # 新增去激活组合键: LT+X
            if LT > 0 and X > 0:
                self.get_logger().info("Deactivation command LT+X received. System returning to PENDING state.")
                self.shared_state.is_activated = False
                return  # 去激活，立即停止处理
                
            # 新增去激活组合键: LT+Y
            if LT > 0 and Y > 0:
                self.get_logger().info("Deactivation command LT+Y received. System returning to PENDING state.")
                self.shared_state.is_activated = False
                return  # 去激活，立即停止处理
                
            # 新增去激活组合键: LT+B
            if LT > 0 and B > 0:
                self.get_logger().info("Deactivation command LT+B received. System returning to PENDING state.")
                self.shared_state.is_activated = False
                return  # 去激活，立即停止处理

            self.shared_state.left_upper_switch_pressed = ((LB and not self.shared_state.left_upper_switch) or self.shared_state.left_upper_switch_pressed)
            self.shared_state.left_lower_switch_pressed = ((LT and not self.shared_state.left_lower_switch) or self.shared_state.left_lower_switch_pressed)
            self.shared_state.right_upper_switch_pressed = ((RB and not self.shared_state.right_upper_switch) or self.shared_state.right_upper_switch_pressed)
            self.shared_state.right_lower_switch_pressed = ((RT and not self.shared_state.right_lower_switch) or self.shared_state.right_lower_switch_pressed)

            # 将摇杆输入直接映射为物理量
            # 左摇杆前后(ly)控制x方向速度
            cmd_x = ly * self.joy_x_scale
            # 左摇杆左右(lx)控制y方向速度
            cmd_y = lx * self.joy_y_scale
            # 右摇杆左右(rx)控制yaw角速度
            cmd_yaw = rx * self.joy_yaw_scale
            
            # 保存映射后的物理量命令（与follow_cmd分开）
            self.shared_state.joy_cmd_x = cmd_x
            self.shared_state.joy_cmd_y = cmd_y
            self.shared_state.joy_cmd_yaw = cmd_yaw
            self.shared_state.joy_cmd_receive_time = current_time  # 使用之前已经获取的时间戳
            
            self.shared_state.left_upper_switch = LB
            self.shared_state.left_lower_switch = LT
            self.shared_state.right_upper_switch = RB
            self.shared_state.right_lower_switch = RT

            # add code
            # 修改触发逻辑
            if self.shared_state.right_lower_switch_pressed:
                # self.get_logger().info("RT button pressed, trigger model switch!")
                msg = Bool()
                msg.data = True
                self.stairs_publisher.publish(msg)  # 发布消息
                self.shared_state.right_lower_switch_pressed = False
                
            if self.shared_state.right_upper_switch_pressed:
                # self.get_logger().info("RB button pressed, trigger model switch!")
                msg = Bool()
                msg.data = True
                self.flat_publisher.publish(msg)  # 发布消息
                self.shared_state.right_upper_switch_pressed = False

    def follow_cmd_callback(self, msg):
        """处理外部速度命令回调"""
        # 只有在激活后才处理速度命令
        if not self.shared_state.is_activated:
            return
            
        with self.shared_state.lock:
            # 外部速度命令直接作为物理量使用，不需要额外缩放
            # 立即更新follow_cmd的接收时间
            current_time = time.monotonic()
            self.shared_state.follow_cmd_receive_time = current_time
            
            # 保存物理量命令
            self.shared_state.follow_cmd_x = msg.linear.x
            self.shared_state.follow_cmd_y = msg.linear.y
            self.shared_state.follow_cmd_yaw = msg.angular.z