import time
import threading
import logging
import numpy as np
import torch
import rclpy
from rclpy.node import Node
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup, ReentrantCallbackGroup
from lowlevel_msg.msg import LowCmd
from rclpy.clock import Clock, ClockType

from him_pkg.utils.shared_state import SharedState
from him_pkg.utils.state_estimator import StateEstimator
from him_pkg.utils.command_profile import RCControllerProfile
from him_pkg.utils.ros_agent import ROSAgent
from him_pkg.utils.history_wrapper import HistoryWrapper
from him_pkg.utils.logger import MultiLogger

class AgentNode(Node):
    """
    专门运行ROSAgent和模型推理的节点
    """
    def __init__(self, shared_state, cfg, policy, cmd_topic="/rl_lowcmd", runtime_env="mujoco", log_root=".", require_button_to_infer=False):
        super().__init__("agent_node")
        
        self.shared_state = shared_state
        self.cfg = cfg
        self.policy = policy
        self.cmd_topic = cmd_topic
        self.runtime_env = runtime_env
        self.log_root = log_root
        self.require_button_to_infer = require_button_to_infer
        
        # 创建状态估计器
        self.state_estimator = StateEstimator(self.shared_state)
        
        # 创建命令配置器
        control_dt = 0.02
        self.command_profile = RCControllerProfile(
            dt=control_dt,
            state_estimator=self.state_estimator
        )
        
        # 创建ROSAgent
        self.hardware_agent = ROSAgent(
            self.cfg, 
            self.state_estimator, 
            self.command_profile, 
            runtime_env=self.runtime_env
        )
        
        # 创建发布者回调组
        self.publisher_callback_group = MutuallyExclusiveCallbackGroup()
        
        # 创建动作发布者
        action_publisher = self.create_publisher(
            LowCmd, 
            self.cmd_topic, 
            1,
            callback_group=self.publisher_callback_group
        )
        
        # 设置动作发布者（这会自动处理 C++ publisher 的创建）
        self.hardware_agent.set_action_publisher(action_publisher, self.cmd_topic)
        
        # 创建定时器回调组
        self.timer_callback_group = MutuallyExclusiveCallbackGroup()
        
        # 定时器将在setup_and_start中创建
        self.clock = Clock(clock_type=ClockType.STEADY_TIME)
        self.timer = None
        
        # 包装agent以支持历史观测
        self.hardware_agent = HistoryWrapper(self.hardware_agent)
        
        # 初始化日志系统
        self.logger = MultiLogger()
        self.logger.add_robot("agent", self.cfg)
        self.init_log_filename()
        
        # 初始化按钮状态和日志记录状态
        self.button_states = np.zeros(4)
        self.is_logging = False
        
        # 控制是否进行日志记录（默认关闭以节省内存）
        self.logging_enabled = False
        
        # 初始化agent内部变量
        self.obs = self.hardware_agent.reset()
        self.step_count = 0
        
        # 退出标志
        self.should_exit = False
        
        # 清理标志
        self._is_cleaned_up = False
        
        # 去激活时保存的关节位置
        self.deactivated_joint_pos = None
        
        # 站立初始化相关参数
        self.stand_up_joint_pos = np.array([
            0.0, 0.76, -1.43,
            0.0, 0.76, -1.43,
            0.0, 0.76, -1.43,
            0.0, 0.76, -1.43], dtype=float)
        self.stand_down_joint_pos = np.array([
            -0.161, 1.19, -2.76,
            0.161, 1.19, -2.76,
            -0.161, 1.19, -2.76,
            0.161, 1.19, -2.76], dtype=float)

        # 站立初始化状态
        self.standing_init_time = 0.0
        self.is_standing_init = False  # 默认需要插值过程
        self.standing_init_duration = 1.5  # 站立过程持续1.5秒
        
        # 推理启动控制状态
        self.inference_started = False
        self.waiting_for_inference_start = False  # 插值完成后设置为True
        
        self.get_logger().info(f"AgentNode initialized, publishing to topic: {self.cmd_topic}")
        
        # 明确的状态说明
        if self.runtime_env == "mujoco":
            self.get_logger().info("Robot behavior: Automatically interpolate from current position to target position")
        else:
            self.get_logger().info("Robot behavior: No interpolation needed (realbot environment)")
        
        if self.require_button_to_infer:
            if self.runtime_env == "mujoco":
                self.get_logger().info("Control flow: LB+A to activate → Auto start interpolation → RT to start inference")
            else:
                self.get_logger().info("Control flow: LB+A to activate → RT to start inference")
        else:
            if self.runtime_env == "mujoco":
                self.get_logger().info("Control flow: LB+A to activate → Auto start interpolation → Auto start inference")
            else:
                self.get_logger().info("Control flow: LB+A to activate → Auto start inference")
            
        self.get_logger().info("Deactivation: LT+A, LT+X, LT+Y or LT+B to deactivate system (highest priority)")
        self.get_logger().info("Memory optimization: Logger is DISABLED by default. Press LT+RT to enable.")
        self.get_logger().info("Logger control: First press = START logging, Second press = STOP and SAVE")
        self.get_logger().info("Button mapping: LB=左上肩键, LT=左下肩键, RB=右上肩键, RT=右下肩键")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self.cleanup()
    
    def cleanup(self):
        """清理所有资源"""
        if self._is_cleaned_up:
            return
            
        try:
            self.shutdown()
            
            # 清理定时器
            if self.timer is not None:
                if not self.timer.is_canceled():
                    self.timer.cancel()
                self.timer = None
            
            # 清理硬件代理
            if hasattr(self, 'hardware_agent') and self.hardware_agent:
                if hasattr(self.hardware_agent, 'cleanup'):
                    self.hardware_agent.cleanup()
                self.hardware_agent = None
            
            # 清理日志系统
            if hasattr(self, 'logger') and self.logger:
                try:
                    # 只有在启用日志记录时才保存最终日志
                    if self.logging_enabled:
                        self.logger.save(self.log_filename)
                    # 彻底清理内存
                    self.logger.clear_memory()
                    if hasattr(self.logger, 'cleanup'):
                        self.logger.cleanup()
                except Exception as e:
                    self.get_logger().error(f"Error saving final log: {e}")
                self.logger = None
            
        except Exception as e:
            if hasattr(self, 'get_logger'):
                self.get_logger().error(f"Error in AgentNode cleanup: {e}")
            else:
                print(f"Error in AgentNode cleanup: {e}")
        finally:
            self._is_cleaned_up = True
    
    def wait_for_activation_and_start(self):
        """主循环：等待激活，运行设置，并处理去激活"""
        while not self.should_exit:
            self.get_logger().info("AgentNode is waiting for system activation (LB+A)...")
            
            # 1. 等待激活
            while not self.shared_state.is_activated and not self.should_exit:
                time.sleep(0.1)
            
            if self.should_exit: break

            # 2. 刚刚被激活，等待并捕获当前关节位置
            self.get_logger().info("System activated! Waiting for current joint positions...")
            
            # 等待几个周期确保获取到最新的关节位置数据
            time.sleep(0.1)  # 等待100ms让lowstate数据更新
            
            with self.shared_state.lock:
                current_joint_pos = self.shared_state.joint_pos.copy()
                self.deactivated_joint_pos = current_joint_pos
                self.get_logger().info(f"Captured current joint positions: {self.deactivated_joint_pos}")
            
            # 3. 运行可中断的设置过程
            setup_successful = self.setup_and_start()

            # 4. 如果设置成功并启动了定时器，则等待去激活
            if setup_successful and self.timer is not None and not self.timer.is_canceled():
                self.get_logger().info("Setup complete, inference running. Waiting for deactivation (LT+A)...")
                while self.shared_state.is_activated and not self.should_exit:
                    time.sleep(0.1)
                
                # 停止定时器（因为被去激活或程序关闭）
                self.timer.cancel()
                self.get_logger().info("Inference timer stopped.")

            # 5. 如果不是程序关闭，则是被去激活，重置状态
            if not self.should_exit:
                self.get_logger().info("System deactivated. Resetting agent state for reactivation.")
                self._reset_for_reactivation()

    def _reset_for_reactivation(self):
        """重置agent的内部状态，为重新激活做准备"""
        self.get_logger().info("Resetting AgentNode state for reactivation.")
        
        # 确保定时器已取消并置None
        if self.timer is not None:
            if not self.timer.is_canceled():
                self.timer.cancel()
            self.timer = None  # 重要：置None避免悬空引用

        # 重置内部状态变量
        self.obs = self.hardware_agent.reset()
        self.step_count = 0
        self.standing_init_time = 0.0
        
        # 根据runtime_env参数决定是否需要执行插值
        if self.runtime_env == "mujoco":
            # mujoco环境需要执行插值
            self.is_standing_init = False  # 需要插值
        else:
            # realbot环境无需执行插值
            self.is_standing_init = True  # 无需插值
        
        # 根据require_button_to_infer参数设置推理启动状态
        if self.require_button_to_infer:
            self.inference_started = False  # 需要按RT键才能开始推理
            self.waiting_for_inference_start = True  # 需要等待RT按钮
        else:
            self.inference_started = True  # 自动开始推理
            self.waiting_for_inference_start = False  # 无需等待按键开始推理
        
        # 确保这个状态被清理，以便下次插值逻辑能正确判断
        self.deactivated_joint_pos = None

    def init_log_filename(self):
        """初始化日志文件名"""
        import os
        datetime = time.strftime("%Y/%m_%d/%H_%M_%S")
        
        for i in range(100):
            try:
                os.makedirs(f"{self.log_root}/{datetime}_{i}")
                self.log_filename = f"{self.log_root}/{datetime}_{i}/log.pkl"
                return
            except FileExistsError:
                continue

    def perform_standing_initialization(self):
        """执行站立初始化序列"""
        if self.is_standing_init:
            return True  # 已经完成站立初始化

        # 根据是否是再激活，决定插值的起始位置
        # 如果 self.deactivated_joint_pos 有值，说明是从一个已知姿态（去激活时的姿态）恢复
        # 否则，就是从默认的趴着姿态开始
        start_pos = self.deactivated_joint_pos if self.deactivated_joint_pos is not None else self.stand_down_joint_pos
        if self.standing_init_time == 0: # 仅在序列开始时打印一次
            self.get_logger().info(f"Position interpolation from: {'Captured current position' if self.deactivated_joint_pos is not None else 'Default lying position'}")

        # 更新站立初始化时间
        self.standing_init_time += 0.02  # 20ms per step
        
        # 在3秒内从起始状态渐变到站立状态
        if self.standing_init_time < self.standing_init_duration:
            # 简单线性插值，参考linear_interpolation_vita01的思路
            ratio = self.standing_init_time / self.standing_init_duration  # [0, 1]
            
            # 创建LowCmd消息
            from lowlevel_msg.msg import LowCmd, MotorCmd
            cmd = LowCmd()
            cmd.head = [0xFE, 0xEF]
            cmd.level_flag = 0xFF
            cmd.gpio = 0
            
            # 初始化电机命令
            cmd.motor_cmd = [MotorCmd() for _ in range(20)]
            
            # 设置前12个关节的位置控制命令
            for i in range(12):
                cmd.motor_cmd[i].mode = 0x01  # (PMSM) mode
                # 线性插值：current = (1 - ratio) * start + ratio * target
                cmd.motor_cmd[i].q = (1 - ratio) * start_pos[i] + ratio * self.stand_up_joint_pos[i]
                cmd.motor_cmd[i].kp = 40.0
                cmd.motor_cmd[i].kd = 4.0
                cmd.motor_cmd[i].dq = 0.0
                cmd.motor_cmd[i].tau = 0.0
            
            # 发布站立命令
            self.hardware_agent.action_publisher.publish(cmd)
            
            # 显示进度（每0.5秒显示一次）
            progress_step = int(self.standing_init_time * 2)  # 每0.5秒一个步骤
            if progress_step != getattr(self, '_last_progress_step', -1):
                self._last_progress_step = progress_step
                self.get_logger().info(f"Position interpolation progress: {(self.standing_init_time/self.standing_init_duration)*100:.1f}%")
            
            return False  # 还未完成
        else:
            # 位置插值完成
            self.is_standing_init = True
            # 清理状态，以便下次正常启动
            self.deactivated_joint_pos = None
            self.get_logger().info("Position interpolation completed")
            
            # 根据require_button_to_infer参数决定是否需要等待RT键启动推理
            if self.require_button_to_infer:
                # 需要RT键触发进入RL运控
                self.waiting_for_inference_start = True
                self.inference_started = False
            else:
                # 自动进入RL运控
                self.waiting_for_inference_start = False
                self.inference_started = True
            
            return True

    def run_step(self):
        """执行一次模型推理和动作发布 - 专注于模型推理"""
        try:
            # 检查是否应该退出
            if self.should_exit:
                self.get_logger().info("Exiting step loop...")
                return
            
            # 获取策略输出的动作
            policy_info = {}
            action = self.policy(self.obs, policy_info)
            
            # 执行一步仿真，获取新的观测值
            obs, ret, done, info = self.hardware_agent.step(action)
            
            # 更新详细信息（与deployment_runner对标）
            info.update(policy_info)
            info.update({
                "observation": obs, 
                "reward": ret, 
                "done": done, 
                "timestep": self.step_count,
                "time": self.step_count * self.hardware_agent.dt if hasattr(self.hardware_agent, 'dt') else self.step_count * 0.02,
                "action": action
            })
            
            # 添加更多状态信息（如果可用）
            if hasattr(self.hardware_agent, 'se') and hasattr(self.hardware_agent.se, 'get_rpy'):
                info.update({"rpy": self.hardware_agent.se.get_rpy()})
            if hasattr(self.hardware_agent, 'torques'):
                info.update({"torques": self.hardware_agent.torques})
            
            # 只有在启用日志记录时才记录日志
            if self.logging_enabled:
                self.logger.log("agent", info)
            
            # 更新观测
            self.obs = obs
            self.step_count += 1
            
            # 每1000步显示一次日志记录状态（约20秒）
            if self.step_count % 1000 == 0:
                log_status = "ENABLED" if self.logging_enabled else "DISABLED"
                self.get_logger().info(f"Step {self.step_count}: Logger status = {log_status}")
            
            # 紧急停止检查（姿态角度检查）
            if hasattr(self.hardware_agent, 'se') and hasattr(self.hardware_agent.se, 'get_rpy'):
                rpy = self.hardware_agent.se.get_rpy()
                if abs(rpy[0]) > 1.6 or abs(rpy[1]) > 1.6:
                    self.get_logger().warn(f"Emergency stop triggered! RPY: {rpy}")
                    self.obs = self.hardware_agent.reset()
            
            # 按钮状态检查和日志记录逻辑
            self.check_button_states()
            
        except Exception as e:
            self.get_logger().error(f"Model inference error: {e}")
            import traceback
            self.get_logger().error(traceback.format_exc())
    
    def check_button_states(self):
        """检查按钮状态并处理日志记录逻辑"""
        try:
            # 保存之前的按钮状态用于检测事件
            prev_button_states = self.button_states[:]
            
            # 获取当前按钮状态（用于其他功能）
            current_buttons = self.command_profile.get_buttons()
            if len(current_buttons) >= 4:
                self.button_states = current_buttons
            
            # LT+RT组合键 - 日志记录
            if (self.shared_state.left_lower_switch_pressed and 
                self.shared_state.right_lower_switch_pressed):
                if not self.is_logging:
                    self.get_logger().info("START LOGGING (LT+RT) - Press LT+RT again to stop and save")
                    self.is_logging = True
                    self.logging_enabled = True  # 开启日志记录
                    if hasattr(self.hardware_agent, 'set_logging'):
                        self.hardware_agent.set_logging(True)
                    self.init_log_filename()
                    self.logger.reset()  # 清空缓存
                else:
                    self.get_logger().info("SAVE LOG AND STOP LOGGING (LT+RT)")
                    self.is_logging = False
                    self.logging_enabled = False  # 关闭日志记录
                    if hasattr(self.hardware_agent, 'set_logging'):
                        self.hardware_agent.set_logging(False)
                    # 保存日志并清空缓存
                    self.logger.save(self.log_filename)
                    self.logger.clear_memory()  # 彻底清理内存
                    self.init_log_filename()
                    self.obs = self.hardware_agent.reset()
                    time.sleep(1)
                # 清除两个按钮的按下状态
                self.shared_state.left_lower_switch_pressed = False
                self.shared_state.right_lower_switch_pressed = False
            
            # 如果不在记录模式，确保日志记录被关闭
            if not self.is_logging:
                self.logging_enabled = False
                
        except Exception as e:
            self.get_logger().error(f"Button state check error: {e}")
    
    def shutdown(self):
        """节点关闭时的清理工作"""
        self.should_exit = True
        # 保存最终日志（只有在启用日志记录时）
        try:
            if self.logging_enabled and hasattr(self, 'logger') and self.logger:
                self.logger.save(self.log_filename)
                self.get_logger().info("Final log saved")
                # 彻底清理内存
                self.logger.clear_memory()
                self.logging_enabled = False
        except Exception as e:
            self.get_logger().error(f"Error saving final log: {e}")
    
    def __del__(self):
        """析构函数，确保日志保存"""
        self.shutdown() 

    def setup_and_start(self):
        """处理所有初始化等待逻辑，然后启动定时器. 如果被中断则返回False"""
        # 检查是否应在开始前中止
        if not self.shared_state.is_activated or self.should_exit:
            return False

        # 根据runtime_env参数决定是否需要执行插值
        if self.runtime_env == "mujoco":
            # mujoco环境需要执行插值
            self.get_logger().info("Automatically starting position interpolation (mujoco environment)...")
            
            # 执行位置插值
            if not self.is_standing_init:
                self.get_logger().info("Executing position interpolation...")
                while not self.is_standing_init:
                    if not self.shared_state.is_activated or self.should_exit:
                        self.get_logger().warn("Setup aborted during position interpolation (deactivated).")
                        return False
                    if self.perform_standing_initialization():
                        break
                    time.sleep(0.02)
        else:
            # realbot环境无需执行插值
            self.is_standing_init = True
            self.get_logger().info("Position interpolation skipped (realbot environment)")
            
            # 设置推理启动状态
            if self.require_button_to_infer:
                self.waiting_for_inference_start = True
                self.inference_started = False
            else:
                self.waiting_for_inference_start = False
                self.inference_started = True
        
        # 等待RT键启动推理（如果需要）
        if self.waiting_for_inference_start:
            self.get_logger().info("Waiting for RT button to start model inference...")
            while self.waiting_for_inference_start:
                if not self.shared_state.is_activated or self.should_exit:
                    self.get_logger().warn("Setup aborted during PHASE 3 (deactivated).")
                    return False
                if self.shared_state.right_lower_switch_pressed:
                    self.inference_started = True
                    self.waiting_for_inference_start = False
                    self.shared_state.right_lower_switch_pressed = False
                    self.get_logger().info("RT button pressed! Model inference STARTED")
                else:
                    time.sleep(0.1)
        elif not self.inference_started:
            # 自动进入RL运控（如果尚未设置）
            self.inference_started = True
            self.get_logger().info("Model inference STARTED automatically")
        
        # 所有准备工作完成
        if not self.shared_state.is_activated or self.should_exit:
            return False

        self.get_logger().info("Setup completed, starting inference timer...")
        self.timer = self.create_timer(
            0.02,
            self.run_step,
            callback_group=self.timer_callback_group,
            clock=self.clock,
        )
        return True 
