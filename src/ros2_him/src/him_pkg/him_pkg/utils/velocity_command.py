#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import time
import argparse
import signal

class VelocityCommander(Node):
    def __init__(self, cmd_vel=0.0, duration=5.0):
        super().__init__('velocity_commander')
        
        self.cmd_vel = cmd_vel
        self.duration = duration
        self.start_time = None
        self.is_running = False
        
        self.publisher = self.create_publisher(
            Twist,
            '/script_cmd',
            10
        )
        
        self.timer = self.create_timer(0.02, self.timer_callback)
        
    def start_command(self):
        self.start_time = time.time()
        self.is_running = True
        
    def timer_callback(self):
        if not self.is_running:
            return
            
        current_time = time.time()
        if current_time - self.start_time >= self.duration:
            self.stop_command()
            return
            
        msg = Twist()
        msg.linear.x = self.cmd_vel
        self.publisher.publish(msg)
        
    def stop_command(self):
        if self.is_running:
            self.is_running = False
            msg = Twist()
            self.publisher.publish(msg)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--velocity', type=float, default=0.5)
    parser.add_argument('--duration', type=float, default=5.0)
    args = parser.parse_args()
    
    rclpy.init()
    commander = VelocityCommander(args.velocity, args.duration)
    
    def signal_handler(sig, frame):
        commander.stop_command()
        rclpy.shutdown()
    
    signal.signal(signal.SIGINT, signal_handler)
    
    commander.start_command()
    rclpy.spin(commander)

if __name__ == '__main__':
    main()