#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import String
import time

class CommandArbitrator(Node):
    def __init__(self):
        super().__init__('command_arbitrator')
        
        # 订阅不同来源的命令
        self.joy_sub = self.create_subscription(
            Twist, 
            '/joy_cmd',  # joy节点发送到这个topic
            self.joy_callback,
            10
        )
        
        self.script_sub = self.create_subscription(
            Twist, 
            '/script_cmd',  # 脚本发送到这个topic
            self.script_callback,
            10
        )
        
        # 发布最终命令
        self.cmd_pub = self.create_publisher(
            Twist,
            '/vel_cmd',  # 最终的命令topic
            10
        )
        
        self.current_source = 'joy'  # 默认使用joy控制
        self.last_joy_time = self.get_clock().now()
        self.last_script_time = self.get_clock().now()
        self.timeout = 0.5  # 超时时间(秒)
        
    def joy_callback(self, msg):
        if self.current_source == 'joy':
            self.last_joy_time = self.get_clock().now()
            self.cmd_pub.publish(msg)
            
    def script_callback(self, msg):
        self.last_script_time = self.get_clock().now()
        self.current_source = 'script'
        self.cmd_pub.publish(msg)
        
    def check_timeout(self):
        current_time = self.get_clock().now()
        if self.current_source == 'script':
            if (current_time - self.last_script_time).nanoseconds / 1e9 > self.timeout:
                self.current_source = 'joy'
                zero_cmd = Twist()
                self.cmd_pub.publish(zero_cmd)

def main():
    rclpy.init()
    arbitrator = CommandArbitrator()
    rclpy.spin(arbitrator)
    rclpy.shutdown()

if __name__ == '__main__':
    main()