#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import String
import time

class CommandArbitrator(Node):
    def __init__(self):
        super().__init__('command_arbitrator')

        # 订阅不同来源的命令
        self.joy_sub = self.create_subscription(
            Twist,
            '/joy_cmd',  # joy节点发送到这个topic
            self.joy_callback,
            10
        )

        self.script_sub = self.create_subscription(
            Twist,
            '/script_cmd',  # 脚本发送到这个topic
            self.script_callback,
            10
        )

        # 发布最终命令
        self.cmd_pub = self.create_publisher(
            Twist,
            '/vel_cmd',  # 最终的命令topic
            10
        )

        self.current_source = 'joy'  # 默认使用joy控制
        self.last_joy_time = self.get_clock().now()
        self.last_script_time = self.get_clock().now()
        self.timeout = 0.5  # 超时时间(秒)

        # 添加定时器来定期检查超时
        self.timer = self.create_timer(0.1, self.check_timeout)  # 10Hz检查频率

        self.get_logger().info("CommandArbitrator started")
        self.get_logger().info(f"  Joy commands: /joy_cmd -> /vel_cmd")
        self.get_logger().info(f"  Script commands: /script_cmd -> /vel_cmd")
        self.get_logger().info(f"  Timeout: {self.timeout}s")
        self.get_logger().info(f"  Current source: {self.current_source}")
        
    def joy_callback(self, msg):
        """处理joy命令回调"""
        self.last_joy_time = self.get_clock().now()

        # 只有在当前源是joy时才转发命令
        if self.current_source == 'joy':
            self.cmd_pub.publish(msg)

    def script_callback(self, msg):
        """处理脚本命令回调"""
        self.last_script_time = self.get_clock().now()

        # 脚本命令具有更高优先级，立即切换到脚本控制
        if self.current_source != 'script':
            self.current_source = 'script'
            self.get_logger().info("Switched to script control")

        self.cmd_pub.publish(msg)

    def check_timeout(self):
        """定期检查脚本命令是否超时"""
        current_time = self.get_clock().now()

        if self.current_source == 'script':
            time_since_last_script = (current_time - self.last_script_time).nanoseconds / 1e9

            if time_since_last_script > self.timeout:
                self.current_source = 'joy'
                self.get_logger().info("Script command timeout, switched back to joy control")

                # 发送零速度命令以确保机器人停止
                zero_cmd = Twist()
                self.cmd_pub.publish(zero_cmd)

def main():
    rclpy.init()
    arbitrator = CommandArbitrator()
    rclpy.spin(arbitrator)
    rclpy.shutdown()

if __name__ == '__main__':
    main()