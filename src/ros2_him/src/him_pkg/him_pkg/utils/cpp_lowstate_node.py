import time
import threading
import numpy as np
import atexit
import weakref
from him_pkg.utils.shared_state import SharedState

# 延迟加载C++模块
_cpp_module = None
_cpp_loaded = False
_active_subscribers = weakref.WeakSet()  # 使用弱引用跟踪活跃的订阅者

def _try_load_cpp_module():
    """尝试加载C++模块，只在第一次调用时执行"""
    global _cpp_module, _cpp_loaded
    if _cpp_loaded:
        return _cpp_module is not None
        
    try:
        # 尝试导入C++实现的订阅者
        from him_pkg.lowstate_subscriber_py import (
            LowStateSubscriber, get_message, message_queue_size, 
            _setup_internal_callback, cleanup
        )
        _cpp_module = {
            'LowStateSubscriber': LowStateSubscriber,
            'get_message': get_message,
            'message_queue_size': message_queue_size,
            '_setup_internal_callback': _setup_internal_callback,
            'cleanup': cleanup
        }
        _cpp_loaded = True
        print("C++ lowstate subscriber module loaded successfully")
        
        # 注册清理函数
        atexit.register(_cleanup_all_subscribers)
        
        return True
    except ImportError as e:
        print(f"Warning: C++ lowstate subscriber not available: {e}")
        _cpp_module = None
        _cpp_loaded = True
        return False

def _cleanup_all_subscribers():
    """清理所有活跃的订阅者"""
    global _cpp_module
    
    try:
        # 停止所有活跃的订阅者
        subscribers_to_cleanup = list(_active_subscribers)
        for subscriber in subscribers_to_cleanup:
            try:
                if hasattr(subscriber, '_cleanup'):
                    subscriber._cleanup()
            except Exception as e:
                print(f"Error cleaning up subscriber: {e}")
        
        # 调用C++清理函数
        if _cpp_module and 'cleanup' in _cpp_module:
            try:
                _cpp_module['cleanup']()
            except Exception as e:
                print(f"Error calling C++ cleanup: {e}")
        
        # 清理全局模块
        _cpp_module = None
        
    except Exception as e:
        print(f"Error in cleanup_all_subscribers: {e}")

# 从原始lowstate_node.py中导入辅助函数
def get_rpy_from_quaternion(q):
    w, x, y, z = q
    r = np.arctan2(2 * (w * x + y * z), 1 - 2 * (x ** 2 + y ** 2))
    p = np.arcsin(2 * (w * y - z * x))
    y = np.arctan2(2 * (w * z + x * y), 1 - 2 * (y ** 2 + z ** 2))
    return np.array([r, p, y])

PI = np.pi

class CppLowStateNode:
    """
    使用C++实现的低级状态节点
    使用消息队列进行线程安全的通信
    """
    def __init__(self, shared_state, state_topic="/rt/lowstate"):
        # 在实际使用时才检查C++模块可用性
        if not _try_load_cpp_module():
            raise RuntimeError("C++ lowstate subscriber is not available")
            
        self.shared_state = shared_state
        self.state_topic = state_topic
        
        # 创建C++订阅者
        try:
            self.cpp_subscriber = _cpp_module['LowStateSubscriber'](self.state_topic)
            
            # 设置内部回调（将消息推入队列）
            _cpp_module['_setup_internal_callback'](self.cpp_subscriber)
            
            # 将此订阅者添加到活跃列表
            _active_subscribers.add(self)
            
        except Exception as e:
            print(f"Error creating C++ subscriber: {e}")
            raise
        
        # 消息处理线程控制
        self.processing_thread = None
        self.stop_processing = threading.Event()
        self._is_started = False
        self._is_cleaned_up = False
        
        print(f"CppLowStateNode initialized, topic: {self.state_topic}")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self._cleanup()
    
    def _cleanup(self):
        """内部清理函数"""
        if self._is_cleaned_up:
            return
            
        try:
            self.stop()
            if self.cpp_subscriber:
                # 释放C++对象
                self.cpp_subscriber = None
        except Exception as e:
            print(f"Error in CppLowStateNode cleanup: {e}")
        finally:
            self._is_cleaned_up = True
        
    def start(self):
        """启动订阅者和消息处理线程"""
        if self._is_cleaned_up:
            raise RuntimeError("Subscriber has been cleaned up")
            
        if self._is_started:
            return
            
        try:
            # 启动C++订阅者
            self.cpp_subscriber.start()
            
            # 启动消息处理线程
            self.stop_processing.clear()
            self.processing_thread = threading.Thread(target=self._message_processing_loop, daemon=True)
            self.processing_thread.start()
            
            self._is_started = True
            print(f"CppLowStateNode started, monitoring topic: {self.state_topic}")
            
        except Exception as e:
            print(f"Error starting CppLowStateNode: {e}")
            self._is_started = False
            raise
        
    def stop(self):
        """停止订阅者和消息处理线程"""
        if not self._is_started:
            return
            
        try:
            # 停止消息处理线程
            if self.processing_thread and self.processing_thread.is_alive():
                self.stop_processing.set()
                self.processing_thread.join(timeout=2.0)
                if self.processing_thread.is_alive():
                    print("Warning: Processing thread did not stop gracefully")
            
            # 停止C++订阅者
            if self.cpp_subscriber:
                self.cpp_subscriber.stop()
            
            self._is_started = False
            print("CppLowStateNode stopped")
            
        except Exception as e:
            print(f"Error stopping CppLowStateNode: {e}")
        
    def is_running(self):
        """检查是否运行中"""
        if self._is_cleaned_up or not self.cpp_subscriber:
            return False
        try:
            return self.cpp_subscriber.is_running()
        except Exception:
            return False
        
    def _message_processing_loop(self):
        """消息处理循环，在Python线程中安全运行"""
        while not self.stop_processing.is_set():
            try:
                # 始终处理消息，保持shared_state数据最新
                # 从队列中获取消息
                msg = _cpp_module['get_message']()
                if msg is not None:
                    self._process_message(msg)
                else:
                    # 没有消息时稍微等待
                    time.sleep(0.001)  # 1ms
            except Exception as e:
                print(f"Error in message processing: {e}")
                time.sleep(0.01)  # 出错时等待10ms
                # 检查是否应该停止
                if self.stop_processing.is_set():
                    break
    
    def _process_message(self, simple_msg):
        """处理来自C++的简化消息"""
        try:
            if not self.shared_state.received_first_legdata:
                self.shared_state.received_first_legdata = True
                print(f"Received first low state data: {time.time() - self.shared_state.init_time}")

            with self.shared_state.lock:
                self.shared_state.input_state_id = simple_msg.state_id
                self.shared_state.input_state_rela_cmd_id = simple_msg.input_cmd_id
                self.shared_state.ts_infer_input_state = int(time.time() * 1000000)

                # 提取电机状态
                self.shared_state.joint_pos = simple_msg.joint_q[:12]  # 确保只取前12个
                self.shared_state.joint_vel = simple_msg.joint_dq[:12]
                self.shared_state.tau_est = simple_msg.joint_tau_est[:12]

                # 提取IMU四元数并转换为欧拉角
                quaternion = list(simple_msg.quaternion)  # [w,x,y,z]
                msg_rpy = get_rpy_from_quaternion(quaternion)
                self.shared_state.euler = np.array(msg_rpy)

                # 计算角速度历史
                idx = self.shared_state.buf_idx % self.shared_state.smoothing_length
                self.shared_state.deuler_history[idx, :] = (
                    self.shared_state.euler - self.shared_state.euler_prev + PI
                ) % (2 * PI) - PI
                
                dt = time.time() - self.shared_state.timuprev
                self.shared_state.dt_history[idx] = dt

                # if dt > 0.005:
                #     print(f'low_state time out: {dt}')

                self.shared_state.timuprev = time.time()
                self.shared_state.buf_idx += 1
                self.shared_state.euler_prev = self.shared_state.euler
                
        except Exception as e:
            print(f"Error processing message: {e}")
    
    def get_queue_size(self):
        """获取消息队列大小（用于调试）"""
        if self._is_cleaned_up or not _cpp_module:
            return 0
        try:
            return _cpp_module['message_queue_size']()
        except Exception:
            return 0

def cleanup_all():
    """手动清理所有资源（可选调用）"""
    _cleanup_all_subscribers() 