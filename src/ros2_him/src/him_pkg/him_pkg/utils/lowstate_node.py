import time
import threading
import logging
import numpy as np
import rclpy
from rclpy.node import Node
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup
from lowlevel_msg.msg import LowState
from scipy.spatial.transform import Rotation as R

from him_pkg.utils.shared_state import SharedState

# 从cheetah_state_estimator_ros.py中导入辅助函数
def get_rpy_from_quaternion(q):
    w, x, y, z = q
    r = np.arctan2(2 * (w * x + y * z), 1 - 2 * (x ** 2 + y ** 2))
    p = np.arcsin(2 * (w * y - z * x))
    y = np.arctan2(2 * (w * z + x * y), 1 - 2 * (y ** 2 + z ** 2))
    return np.array([r, p, y])

PI = np.pi
RPY_EXTRINSIC = np.array([1.62532906, -0.12832938, -1.61257362])

class LowStateNode(Node):
    """
    专门处理机器人低级状态(LowState)的节点
    """
    def __init__(self, shared_state, state_topic="/rt/lowstate"):
        super().__init__("lowstate_node")
        
        self.shared_state = shared_state
        self.state_topic = state_topic
        
        # 创建回调组，保证回调在同一线程中执行
        self.callback_group = MutuallyExclusiveCallbackGroup()
        
        # 订阅机器人状态话题
        self.lowstate_sub = self.create_subscription(
            LowState,
            self.state_topic,
            self.lowstate_callback,
            1,
            callback_group=self.callback_group
        )
        
        # 初始化外部旋转矩阵
        self.rot_extrinsic = R.from_euler('xyz', RPY_EXTRINSIC, degrees=True).as_matrix()
        
        self.get_logger().info(f"LowStateNode 已启动，监听话题: {self.state_topic}")
        
    def lowstate_callback(self, msg):
        """处理机器人低级状态回调"""
        if not self.shared_state.received_first_legdata:
            self.shared_state.received_first_legdata = True
            self.get_logger().info(f"接收到第一个低级状态数据: {time.time() - self.shared_state.init_time}")

        with self.shared_state.lock:
            self.shared_state.input_state_id = msg.state_id
            self.shared_state.input_state_rela_cmd_id = msg.input_cmd_id
            self.shared_state.ts_infer_input_state = int(time.time() * 1000000)

            self.shared_state.joint_pos = [motor.q for motor in msg.motor_state[0:12]]
            self.shared_state.joint_vel = [motor.dq for motor in msg.motor_state[0:12]]
            self.shared_state.tau_est = [motor.tau_est for motor in msg.motor_state[0:12]]

            quaternion = [
                msg.imu_state.quaternion[0], 
                msg.imu_state.quaternion[1], 
                msg.imu_state.quaternion[2], 
                msg.imu_state.quaternion[3]
            ]

            msg_rpy = get_rpy_from_quaternion(quaternion)  # [w,x,y,z] -> r,p,y
            self.shared_state.euler = np.array(msg_rpy)

            idx = self.shared_state.buf_idx % self.shared_state.smoothing_length
            self.shared_state.deuler_history[idx, :] = (
                self.shared_state.euler - self.shared_state.euler_prev + PI
            ) % (2 * PI) - PI
            
            dt = time.time() - self.shared_state.timuprev
            self.shared_state.dt_history[idx] = dt

            if dt > 0.005:
                self.get_logger().warning(f'low_state time out: {dt}')

            self.shared_state.timuprev = time.time()
            self.shared_state.buf_idx += 1
            self.shared_state.euler_prev = self.shared_state.euler
            
            # # 记录线程ID
            # if self.shared_state.buf_idx % 100 == 0:
            #     current_thread = threading.current_thread()
            #     self.get_logger().info(
            #         f"LowState回调运行在线程: {current_thread.name} (ID: {current_thread.ident})"
            #     ) 