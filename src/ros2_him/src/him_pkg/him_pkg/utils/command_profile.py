import torch


class RCControllerProfile:
    def __init__(self, dt, state_estimator, max_time_s=10.):
        self.dt = dt
        self.max_timestep = int(max_time_s / self.dt)
        self.state_estimator = state_estimator
        
        # 安全检查CUDA是否可用
        cuda_available = False
        try:
            cuda_available = torch.cuda.is_available()
            if cuda_available:
                # 尝试一个简单的CUDA操作来验证PyTorch是否支持CUDA
                test_tensor = torch.zeros(1, device="cuda")
        except (AssertionError, RuntimeError):
            cuda_available = False
        
        self.device = "cuda" if cuda_available else "cpu"
        
        # 初始化命令存储
        try:
            self.commands = torch.zeros((self.max_timestep, 9), device=self.device)
        except Exception as e:
            print(f"无法在设备 {self.device} 上创建张量: {e}")
            print("回退到CPU")
            self.device = "cpu"
            self.commands = torch.zeros((self.max_timestep, 9), device=self.device)
        
        # 预先创建一个命令张量，避免每次get_command都创建新的
        try:
            self.command_tensor = torch.zeros(3, device=self.device)
        except Exception as e:
            print(f"创建命令张量错误: {e}")
            self.device = "cpu"
            self.command_tensor = torch.zeros(3, device="cpu")
            
        self.start_time = 0
        self.triggered_commands = {i: None for i in range(4)}  # command profiles for each action button on the controller
        self.currently_triggered = [0, 0, 0, 0]
        self.button_states = [0, 0, 0, 0]

    def get_command(self, t):
        # 获取命令和按钮状态
        command, buttons = self.state_estimator.get_command()
        
        # 使用预先创建的张量，避免每次都创建和检查设备
        if isinstance(command, torch.Tensor):
            # 如果已经是tensor，直接复制值
            self.command_tensor.copy_(command)
        else:
            # 如果不是tensor，直接赋值
            self.command_tensor[0] = command[0]
            self.command_tensor[1] = command[1]
            self.command_tensor[2] = command[2]
            
        # 命令已经是物理量，不需要额外缩放

        reset_timer = False

        # 检查动作按钮
        prev_button_states = self.button_states[:]
        self.button_states = buttons
        for button in range(4):
            if self.triggered_commands[button] is not None:
                if self.button_states[button] == 1 and prev_button_states[button] == 0:
                    if not self.currently_triggered[button]:
                        # 重置触发动作
                        self.triggered_commands[button].reset(t)
                        # 重置内部计时变量
                        reset_timer = True
                        self.currently_triggered[button] = True
                    else:
                        self.currently_triggered[button] = False
                # 执行触发的动作
                if self.currently_triggered[button] and t < self.triggered_commands[button].max_timestep:
                    return self.triggered_commands[button].get_command(t), reset_timer

        return self.command_tensor, reset_timer

    def add_triggered_command(self, button_idx, command_profile):
        self.triggered_commands[button_idx] = command_profile

    def get_buttons(self):
        return self.state_estimator.get_buttons()
        
    def reset(self, reset_time):
        self.start_time = reset_time


if __name__ == "__main__":
    # 简单测试代码
    from unittest.mock import MagicMock
    mock_state_estimator = MagicMock()
    mock_state_estimator.get_command.return_value = ([1.0, 0.0, 0.0], [0, 0, 0, 0])
    mock_state_estimator.get_buttons.return_value = [0, 0, 0, 0]
    
    cmdprof = RCControllerProfile(dt=0.2, state_estimator=mock_state_estimator)
    print(cmdprof.commands)
    print(cmdprof.get_command(2))
