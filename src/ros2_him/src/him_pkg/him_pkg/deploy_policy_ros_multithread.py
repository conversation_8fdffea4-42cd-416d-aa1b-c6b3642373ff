#!/usr/bin/env python3
###################################################################
# Copyright (C) 2025 VitaDynamics. All rights reserved.
#
# Filename: deploy_policy_ros_multithread.py
# Author : siyu.zhou
# Date : Jun, 2025
# Describe: 实现使用多线程方式部署深度强化学习控制器
###################################################################

import argparse
import glob
import os
import pickle as pkl
import threading
import time
import logging
import sys
import signal
import atexit

import numpy as np
import torch

import rclpy
from rclpy.executors import SingleThreadedExecutor
from ament_index_python.packages import get_package_share_directory
# add code
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup
from std_msgs.msg import Bool # 导入 Bool 消息类型


# 导入自定义模块
from him_pkg.utils.shared_state import SharedState
from him_pkg.utils.lowstate_node import LowStateNode
from him_pkg.utils.cpp_lowstate_node import CppLowStateNode
from him_pkg.utils.joy_node import JoyNode
from him_pkg.utils.agent_node import AgentNode

logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s: %(message)s')

from rclpy.node import Node

class ModelSwitchNode(Node):
    def __init__(self, policy_a, policy_b, current_policy_ref):
        super().__init__('model_switch_node')
        self.policy_a = policy_a
        self.policy_b = policy_b
        self.current_policy_ref = current_policy_ref
        
        self.stairs_subscription = self.create_subscription(
            Bool,
            'stairsmodel_switch_trigger',
            self.stairsmodel_switch_callback,
            10)
        
        self.flat_subscription = self.create_subscription(
            Bool,
            'flatmodel_switch_trigger',
            self.flatmodel_switch_callback,
            10)
        
    def stairsmodel_switch_callback(self, msg):
        if msg.data:
            if self.current_policy_ref[0] == self.policy_a:
                self.current_policy_ref[0] = self.policy_b
                self.get_logger().info('Switch to model B~~~~~~~~~~~')
    
    def flatmodel_switch_callback(self, msg):
        if msg.data:
            if self.current_policy_ref[0] == self.policy_b:
                self.current_policy_ref[0] = self.policy_a
                self.get_logger().info('Switch to model A~~~~~~~~~~~')



def load_policy(logdir, model_type="pt", device="cpu", save_iteration=False):
    """
    加载策略模型
    """
    # 模型路径
    pt_path = os.path.join(logdir, "policy.pt")
    onnx_path = os.path.join(logdir, "policy.onnx")
    hbm_path = os.path.join(logdir, "policy.hbm")
    
    # 只有在save_iteration为True时才创建保存目录和相关文件
    save_dir = None
    if save_iteration:
        # 创建保存模型输入输出的目录
        save_dir = os.path.join(logdir, "model_io_data")
        os.makedirs(save_dir, exist_ok=True)
        
        # 创建元数据文件，记录数据格式信息
        metadata_file = os.path.join(save_dir, "metadata.txt")
        if not os.path.exists(metadata_file):
            with open(metadata_file, "w") as f:
                f.write(f"模型类型: {model_type}\n")
                f.write(f"模型路径: {logdir}\n")
                f.write("数据格式: float32 二进制\n")
                f.write("文件命名格式: model_input_XXXXX.bin, model_output_XXXXX.bin，其中XXXXX为迭代次数\n")
                f.write("保存时间: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n\n")
    
    if model_type == "hbm":
        if not os.path.exists(hbm_path):
            raise FileNotFoundError(f"指定加载HBM模型，但未找到模型文件：{hbm_path}")
        
        try:
            from him_pkg import libmodel_task
            model = libmodel_task.ModelTask()
            model.ModelInit(hbm_path)
            
            def policy(obs, info):
                # 准备HBM模型的输入
                input_data = obs["obs_history"].cpu().numpy()
                # 如果输入是批量数据，需要确保形状正确
                if len(input_data.shape) == 1:
                    input_data = input_data.reshape(1, -1)
                
                # 运行HBM模型
                input_data_list = [input_data]
                action = model.ModelInfer(input_data_list)
                action = np.array(action)
                
                # 确保输出形状正确
                if len(action.shape) == 1:
                    action = action.reshape(1, -1)
                
                return torch.tensor(action)
            
            logging.info("成功加载HBM模型: " + hbm_path)
            return policy
        except Exception as e:
            logging.error(f"加载指定的HBM模型失败: {e}")
            raise
            
    elif model_type == "pt":
        if not os.path.exists(pt_path):
            raise FileNotFoundError(f"指定加载PyTorch模型，但在{logdir}中未找到policy.pt文件")
        
        try:
            policy_net = torch.jit.load(pt_path, map_location=device)
            policy_net.eval()
            
            def policy(obs, info):
                # 确保输入数据在正确的设备上
                obs_tensor = obs["obs_history"].to(device)
                action = policy_net.forward(obs_tensor)
                return action.cpu()
            
            logging.info("成功加载PyTorch模型: " + pt_path)
            return policy
        except Exception as e:
            logging.error(f"加载指定的PyTorch模型失败: {e}")
            raise
            
    elif model_type == "onnx":
        if not os.path.exists(onnx_path):
            raise FileNotFoundError(f"指定加载ONNX模型，但在{logdir}中未找到policy.onnx文件")

        try:
            import onnxruntime as ort
            providers = []
            if "cuda" in device and "CUDAExecutionProvider" in ort.get_available_providers():
                providers.append("CUDAExecutionProvider")
            providers.append("CPUExecutionProvider")
            
            logging.info(f"ONNX使用提供者: {providers}")
            session = ort.InferenceSession(onnx_path, providers=providers)
            input_name = session.get_inputs()[0].name
            
            def policy(obs, info):
                # 准备ONNX模型的输入
                input_data = obs["obs_history"].cpu().numpy()
                # 如果输入是批量数据，需要确保形状正确
                if len(input_data.shape) == 1:
                    input_data = input_data.reshape(1, -1)

                # 运行ONNX模型
                onnx_inputs = {input_name: input_data}
                action = session.run(None, onnx_inputs)[0]

                return torch.tensor(action)
            
            logging.info("成功加载ONNX模型: " + onnx_path)
            return policy
        except ImportError:
            logging.error("无法导入onnxruntime，请确保已安装该库")
            logging.error("请安装onnxruntime: pip install onnxruntime")
            raise
        except Exception as e:
            logging.error(f"加载指定的ONNX模型失败: {e}")
            raise
    
    # 如果没有找到指定类型的模型或模型类型不支持，抛出异常
    raise FileNotFoundError(f"在{logdir}中未找到指定类型({model_type})的模型文件，或该类型不支持。请检查模型索引({os.path.basename(logdir)})是否正确。")


def cleanup_handler():
    """程序退出时的清理处理器"""
    try:
        from him_pkg.utils.cpp_lowstate_node import cleanup_all as cleanup_lowstate
        from him_pkg.utils.cpp_lowcmd_node import cleanup_all as cleanup_lowcmd
        cleanup_lowstate()
        cleanup_lowcmd()
        logging.info("C++ resources cleaned up by exit handler")
    except Exception as e:
        logging.error(f"Error in cleanup handler: {e}")

def main(args=None):
    # 注册退出清理处理器
    atexit.register(cleanup_handler)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="多线程运行HIM控制器")
    parser.add_argument("--model_type", type=str, default="pt", 
                       choices=["pt", "onnx", "hbm"], 
                       help="模型类型：pt或onnx或hbm (默认: pt)")
    parser.add_argument("--save_iteration", action="store_true", 
                       help="是否保存迭代数据 (默认: False)")
    parser.add_argument("--runtime_env", type=str, default="mujoco", 
                       choices=["realbot", "mujoco"],
                       help="运行时环境: realbot或mujoco (默认: mujoco)")
    parser.add_argument("--require_rt_to_infer", action="store_true",
                       help="是否需要RT键触发进入RL运控 (默认: False)")
    parser.add_argument("--log_root", type=str, default="./logs",
                       help="日志根目录 (默认: ./logs)")
    # parser.add_argument("--model_idx", type=str, default="Jun18_21-55-35_ckpt6000",
    #                    help="模型索引，指定要加载的模型 (默认: Jun18_21-55-35_ckpt6000)")
    parser.add_argument("--model_idx_a", type=str, default="Jul29_16-33-45_ckpt20000",
                       help="模型A索引, 指定要加载的模型 (默认: Jul29_16-33-45_ckpt20000)")
    parser.add_argument("--model_idx_b", type=str, default="Jun18_21-55-35_ckpt6000",
                       help="模型B索引, 指定要加载的模型 (默认: Jun18_21-55-35_ckpt6000)")
    parsed_args, unknown = parser.parse_known_args(args)
    
    # 初始化rclpy
    rclpy.init(args=unknown)
    
    # 创建共享状态对象
    shared_state = SharedState()
    
    # 设置环境相关参数
    runtime_env = parsed_args.runtime_env
    if runtime_env == "realbot":
        state_topic = "/rt/lowstate"
        cmd_topic = "/rl_lowcmd"
    else:  # mujoco
        state_topic = "/sim/lowstate"
        cmd_topic = "/sim/lowcmd"
    
    # 检查CUDA可用性
    device = "cpu"
    cuda_available = False
    try:
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            test_tensor = torch.zeros(1, device="cuda")
            device = "cuda"
    except (AssertionError, RuntimeError):
        pass
    
    logging.info(f"使用设备: {device}")
    
    # 加载环境配置
    package_share_dir = get_package_share_directory('him_pkg')
    label = "gait-conditioned-agility/exported/"
    # model_idx = parsed_args.model_idx
    model_idx_a = parsed_args.model_idx_a
    model_idx_b = parsed_args.model_idx_b
    dirs = os.path.join(package_share_dir, "runs", label)
    # logdir = os.path.join(dirs, model_idx)
    logdir_a = os.path.join(dirs, model_idx_a)
    logdir_b = os.path.join(dirs, model_idx_b)
    
    # with open(os.path.join(logdir, "env_cfg.pkl"), "rb") as file:
    #     cfg = pkl.load(file)
    #     logging.info(f"配置文件包含键: {list(cfg.keys())}")
    
    # logging.info(f"配置文件成功加载: {os.path.join(logdir, 'env_cfg.pkl')}")
    
    with open(os.path.join(logdir_a, "env_cfg.pkl"), "rb") as file:
        cfg = pkl.load(file)
        logging.info(f"配置文件包含键: {list(cfg.keys())}")
    
    logging.info(f"配置文件成功加载: {os.path.join(logdir_a, 'env_cfg.pkl')}")

    # # 加载策略模型
    # policy = load_policy(
    #     logdir=logdir, 
    #     model_type=parsed_args.model_type,
    #     device=device,
    #     save_iteration=parsed_args.save_iteration
    # )

    # 加载策略模型
    policy_a = load_policy(
        logdir=logdir_a, 
        model_type=parsed_args.model_type,
        device=device,
        save_iteration=parsed_args.save_iteration
    )
    policy_b = load_policy(
        logdir=logdir_b, 
        model_type=parsed_args.model_type,
        device=device,
        save_iteration=parsed_args.save_iteration
    )

    # 使用列表包装current_policy以通过引用传递
    current_policy_ref = [policy_a]
    
    # 显示机器人初始化参数
    logging.info(f"机器人初始化参数:")
    logging.info(f"  模型类型: {parsed_args.model_type}")
    # logging.info(f"  模型索引: {parsed_args.model_idx}")
    logging.info(f"  模型A索引: {parsed_args.model_idx_a}")
    logging.info(f"  模型B索引: {parsed_args.model_idx_b}")

    logging.info(f"  保存迭代数据: {parsed_args.save_iteration}")
    if parsed_args.runtime_env == "mujoco":
        logging.info(f"  机器人行为: 按下LB+A后，自动执行插值程序，让机器人站起来")
    else:
        logging.info(f"  机器人行为: 按下LB+A后，无须执行插值程序")
    if parsed_args.require_rt_to_infer:
        logging.info(f"  推理启动方式: RT键触发进入RL运控")
    else:
        logging.info(f"  推理启动方式: 自动进入RL运控")
    logging.info(f"  运行时环境: {parsed_args.runtime_env}")
    logging.info(f"  日志根目录: {parsed_args.log_root}")
    
    # 启动前提示
    logging.info("-" * 50)
    logging.info("System is PENDING. Press LB+A on the controller to activate.")
    logging.info("-" * 50)
    
    # 创建各个节点
    lowstate_node = CppLowStateNode(shared_state, state_topic=state_topic)
    lowstate_node.start()
    joy_node = JoyNode(shared_state)
    # agent_node = AgentNode(
    #     shared_state=shared_state,
    #     cfg=cfg,
    #     policy=policy,
    #     cmd_topic=cmd_topic,
    #     runtime_env=runtime_env,
    #     log_root=parsed_args.log_root,
    #     require_button_to_infer=parsed_args.require_rt_to_infer  # 根据参数决定是否需要RT键启动推理
    # )
    agent_node = AgentNode(
        shared_state=shared_state,
        cfg=cfg,
        policy=lambda obs, info: current_policy_ref[0](obs, info),
        cmd_topic=cmd_topic,
        runtime_env=runtime_env,
        log_root=parsed_args.log_root,
        require_button_to_infer=parsed_args.require_rt_to_infer  # 根据参数决定是否需要RT键启动推理
    )

    # 创建节点时传入策略模型
    model_switch_node = ModelSwitchNode(policy_a, policy_b, current_policy_ref)
    
    # 在独立线程中启动agent的主循环
    setup_thread = threading.Thread(
        target=agent_node.wait_for_activation_and_start,
        daemon=True,
        name="Agent_Lifecycle_Thread"
    )
    setup_thread.start()
    
    # 创建执行器 - CppLowStateNode不需要执行器，它有自己的线程
    joy_executor = SingleThreadedExecutor()
    agent_executor = SingleThreadedExecutor()
    
    # 将标准ROS2节点添加到对应的执行器
    joy_executor.add_node(joy_node)
    agent_executor.add_node(agent_node)

    executor = SingleThreadedExecutor() # 创建一个执行器
    # 将标准ROS2节点添加到对应的执行器
    joy_executor.add_node(joy_node)
    agent_executor.add_node(agent_node)
    executor.add_node(model_switch_node) # 添加订阅者节点到执行器
    
    # 创建线程并启动执行器 - 只为标准ROS2节点创建线程
    joy_thread = threading.Thread(
        target=joy_executor.spin,
        daemon=True,
        name="Joy_Thread"
    )
    agent_thread = threading.Thread(
        target=agent_executor.spin,
        daemon=True,
        name="Agent_Thread"
    )
    executor_thread = threading.Thread(target=executor.spin, daemon=True, name="Executor_Thread") # 创建一个线程来运行执行器
    
    # 启动线程
    joy_thread.start()
    agent_thread.start()

    executor_thread.start() # 启动线程
    
    logging.info("All nodes started, running...")
    
    # 定义退出标志
    should_exit = False
    
    # 定义信号处理函数
    def signal_handler(sig, frame):
        nonlocal should_exit
        logging.info(f"Received signal {sig}, safely exiting...")
        should_exit = True
        # 通知agent_node退出
        agent_node.should_exit = True
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # kill命令默认信号
    
    try:
        # 主线程等待，直到收到退出信号
        while not should_exit:
            time.sleep(0.1)
    except KeyboardInterrupt:
        logging.info("Received keyboard interrupt, exiting...")
    finally:
        # 清理资源
        logging.info("Shutting down nodes and executors...")
        
        # 通知agent_node安全退出并保存日志
        if 'agent_node' in locals():
            agent_node.shutdown()
        
        # 停止C++低级状态节点
        if 'lowstate_node' in locals():
            lowstate_node.stop()
        
        # 添加C++清理调用
        try:
            from him_pkg.utils.cpp_lowstate_node import cleanup_all as cleanup_lowstate
            from him_pkg.utils.cpp_lowcmd_node import cleanup_all as cleanup_lowcmd
            cleanup_lowstate()
            cleanup_lowcmd()
            logging.info("C++ resources cleaned up")
        except Exception as e:
            logging.error(f"Error cleaning C++ resources: {e}")
        
        # 清理策略模型
        try:
            # if 'policy' in locals():
            #     del policy
            if 'policy' in locals():
                del policy_a
                del policy_b
            # 如果使用GPU，清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            logging.info("Policy model cleaned up")
        except Exception as e:
            logging.error(f"Error cleaning policy model: {e}")
        
        # 关闭标准ROS2节点的执行器
        if 'joy_executor' in locals():
            joy_executor.shutdown()
        if 'agent_executor' in locals():
            agent_executor.shutdown()

        if 'executor' in locals():
            executor.shutdown()
        
        # 销毁标准ROS2节点
        if 'joy_node' in locals():
            joy_node.destroy_node()
        if 'agent_node' in locals():
            agent_node.destroy_node()
        
        if 'model_switch_sub' in locals():
            rclpy.destroy_subscription(model_switch_sub)

        rclpy.shutdown()
        
        # 等待线程结束 - 改进等待逻辑
        logging.info("Waiting for all threads to finish...")
        threads_to_wait = []
        if 'joy_thread' in locals():
            threads_to_wait.append((joy_thread, "Joy"))
        if 'agent_thread' in locals():
            threads_to_wait.append((agent_thread, "Agent"))
        if 'setup_thread' in locals():
            threads_to_wait.append((setup_thread, "Setup"))
        if 'executor_thread' in locals():
            threads_to_wait.append((executor_thread, "Executor"))
        
        for thread, name in threads_to_wait:
            if thread.is_alive():
                thread.join(timeout=5.0)
                if thread.is_alive():
                    logging.warning(f"{name} thread did not stop gracefully")
        
        logging.info("Program safely exited")


if __name__ == "__main__":
    main() 