#include <pybind11/pybind11.h>
#include <pybind11/functional.h>
#include <pybind11/stl.h>
#include <pybind11/chrono.h>
#include "him_pkg/lowstate_subscriber.hpp"
#include <lowlevel_msg/msg/low_state.hpp>
#include <queue>
#include <mutex>
#include <memory>

namespace py = pybind11;

// 简化的消息数据结构
struct SimpleLowState {
    uint16_t state_id;
    uint16_t input_cmd_id;
    std::array<float, 4> quaternion;
    std::vector<float> joint_q;
    std::vector<float> joint_dq;
    std::vector<float> joint_tau_est;
};

// 消息队列，用于线程间安全传递消息
class MessageQueue {
public:
    void push(const SimpleLowState& msg) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() > 10) {  // 限制队列大小
            queue_.pop();
        }
        queue_.push(msg);
    }
    
    bool pop(SimpleLowState& msg) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return false;
        }
        msg = queue_.front();
        queue_.pop();
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!queue_.empty()) {
            queue_.pop();
        }
    }

private:
    mutable std::mutex mutex_;
    std::queue<SimpleLowState> queue_;
};

// 使用智能指针管理全局消息队列，确保正确清理
static std::unique_ptr<MessageQueue> g_message_queue = std::make_unique<MessageQueue>();

// 转换ROS2消息为简化格式
SimpleLowState convert_ros_message(const lowlevel_msg::msg::LowState::SharedPtr msg) {
    SimpleLowState simple_msg;
    
    simple_msg.state_id = msg->state_id;
    simple_msg.input_cmd_id = msg->input_cmd_id;
    
    // IMU四元数
    for (size_t i = 0; i < 4 && i < msg->imu_state.quaternion.size(); ++i) {
        simple_msg.quaternion[i] = msg->imu_state.quaternion[i];
    }
    
    // 电机状态（只取前12个）
    simple_msg.joint_q.clear();
    simple_msg.joint_dq.clear();
    simple_msg.joint_tau_est.clear();
    
    for (size_t i = 0; i < std::min(static_cast<size_t>(12), msg->motor_state.size()); ++i) {
        simple_msg.joint_q.push_back(msg->motor_state[i].q);
        simple_msg.joint_dq.push_back(msg->motor_state[i].dq);
        simple_msg.joint_tau_est.push_back(msg->motor_state[i].tau_est);
    }
    
    return simple_msg;
}

PYBIND11_MODULE(lowstate_subscriber_py, m) {
    m.doc() = "Low State Subscriber C++ implementation with pybind11";
    
    // 绑定简化的消息结构
    py::class_<SimpleLowState>(m, "SimpleLowState")
        .def_readwrite("state_id", &SimpleLowState::state_id)
        .def_readwrite("input_cmd_id", &SimpleLowState::input_cmd_id)
        .def_readwrite("quaternion", &SimpleLowState::quaternion)
        .def_readwrite("joint_q", &SimpleLowState::joint_q)
        .def_readwrite("joint_dq", &SimpleLowState::joint_dq)
        .def_readwrite("joint_tau_est", &SimpleLowState::joint_tau_est);
    
    // 绑定LowStateSubscriber类
    py::class_<him_pkg::LowStateSubscriber>(m, "LowStateSubscriber")
        .def(py::init<const std::string&>(), 
             py::arg("topic_name") = "/rt/lowstate",
             "Initialize LowState subscriber")
        .def("start", &him_pkg::LowStateSubscriber::start,
             "Start the subscriber")
        .def("stop", &him_pkg::LowStateSubscriber::stop,
             "Stop the subscriber")
        .def("is_running", &him_pkg::LowStateSubscriber::is_running,
             "Check if subscriber is running");
    
    // 绑定消息队列访问函数
    m.def("get_message", []() -> py::object {
        if (!g_message_queue) {
            return py::none();
        }
        SimpleLowState msg;
        if (g_message_queue->pop(msg)) {
            return py::cast(msg);
        }
        return py::none();
    }, "Get a message from the queue");
    
    m.def("message_queue_size", []() {
        if (!g_message_queue) {
            return size_t(0);
        }
        return g_message_queue->size();
    }, "Get the size of the message queue");
    
    // 清理函数，用于模块卸载时清理资源
    m.def("cleanup", []() {
        if (g_message_queue) {
            g_message_queue->clear();
            g_message_queue.reset();
        }
    }, "Clean up resources");
    
    // 内部函数：设置C++回调（将消息推入队列）
    m.def("_setup_internal_callback", [](him_pkg::LowStateSubscriber& subscriber) {
        subscriber.set_callback([](const lowlevel_msg::msg::LowState::SharedPtr msg) {
            if (g_message_queue) {
                SimpleLowState simple_msg = convert_ros_message(msg);
                g_message_queue->push(simple_msg);
            }
        });
    }, "Internal function to setup C++ callback");
} 