#include "him_pkg/lowstate_subscriber.hpp"
#include <thread>
#include <atomic>
#include <mutex>

namespace him_pkg {

// 静态变量来跟踪ROS2初始化状态
static std::atomic<bool> ros2_initialized_by_us{false};
static std::mutex ros2_init_mutex;

LowStateSubscriber::LowStateSubscriber(const std::string& topic_name)
    : topic_name_(topic_name), running_(false) {
    
    std::lock_guard<std::mutex> lock(ros2_init_mutex);
    // 如果ROS2没有初始化，就初始化它
    // 这样可以确保C++和Python都能使用ROS2
    if (!rclcpp::ok()) {
        rclcpp::init(0, nullptr);
        ros2_initialized_by_us.store(true);
    }
    
    try {
        // 创建节点，使用默认上下文
        node_ = rclcpp::Node::make_shared("lowstate_subscriber_cpp");
        
        // 创建执行器
        executor_ = std::make_shared<rclcpp::executors::SingleThreadedExecutor>();
        executor_->add_node(node_);
        
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("Failed to create ROS2 node: ") + e.what());
    }
}

LowStateSubscriber::~LowStateSubscriber() {
    stop();
    
    // 清理执行器和节点的循环引用
    if (executor_ && node_) {
        try {
            executor_->remove_node(node_);
        } catch (const std::exception& e) {
            // 忽略移除节点时的错误，确保清理过程继续
        }
    }
    
    // 清理shared_ptr
    executor_.reset();
    node_.reset();
    
    // 如果是我们初始化的ROS2，则关闭它
    std::lock_guard<std::mutex> lock(ros2_init_mutex);
    if (ros2_initialized_by_us.load()) {
        if (rclcpp::ok()) {
            rclcpp::shutdown();
        }
        ros2_initialized_by_us.store(false);
    }
}

void LowStateSubscriber::set_callback(CallbackType callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    python_callback_ = callback;
}

void LowStateSubscriber::start() {
    if (running_.load()) {
        return;
    }
    
    if (!rclcpp::ok()) {
        throw std::runtime_error("ROS2 is not running! Cannot start subscriber.");
    }
    
    try {
        // 创建订阅者
        subscription_ = node_->create_subscription<lowlevel_msg::msg::LowState>(
            topic_name_,
            1,  // QoS depth
            [this](const lowlevel_msg::msg::LowState::SharedPtr msg) {
                this->lowstate_callback(msg);
            }
        );
        
        running_.store(true);
        
        // 在单独线程中启动spin
        spin_thread_ = std::thread([this]() {
            while (running_.load() && rclcpp::ok()) {
                try {
                    // 使用较长的超时时间减少CPU使用
                    executor_->spin_once(std::chrono::milliseconds(10));
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(node_->get_logger(), "Error in spin_once: %s", e.what());
                    break;
                }
            }
        });
        
    } catch (const std::exception& e) {
        running_.store(false);
        throw std::runtime_error(std::string("Failed to start subscriber: ") + e.what());
    }
}

void LowStateSubscriber::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    
    // 等待spin线程结束
    if (spin_thread_.joinable()) {
        spin_thread_.join();
    }
    
    // 清理订阅者
    if (subscription_) {
        subscription_.reset();
    }
    
    // 清理回调
    {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        python_callback_ = nullptr;
    }
}

bool LowStateSubscriber::is_running() const {
    return running_.load();
}

void LowStateSubscriber::lowstate_callback(const lowlevel_msg::msg::LowState::SharedPtr msg) {
    // 安全地调用Python回调函数
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (python_callback_) {
        try {
            python_callback_(msg);
        } catch (const std::exception& e) {
            RCLCPP_ERROR(node_->get_logger(), "Error in Python callback: %s", e.what());
        }
    }
}

} // namespace him_pkg 