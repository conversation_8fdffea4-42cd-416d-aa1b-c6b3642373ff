#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include "him_pkg/lowcmd_publisher.hpp"

namespace py = pybind11;

PYBIND11_MODULE(lowcmd_publisher_py, m) {
    m.doc() = "Low Cmd Publisher C++ implementation with pybind11";
    
    // 绑定简化的命令结构
    py::class_<him_pkg::SimpleLowCmd>(m, "SimpleLowCmd")
        .def(py::init<>())
        .def_readwrite("motor_q", &him_pkg::SimpleLowCmd::motor_q)
        .def_readwrite("motor_dq", &him_pkg::SimpleLowCmd::motor_dq)
        .def_readwrite("motor_kp", &him_pkg::SimpleLowCmd::motor_kp)
        .def_readwrite("motor_kd", &him_pkg::SimpleLowCmd::motor_kd)
        .def_readwrite("motor_tau", &him_pkg::SimpleLowCmd::motor_tau)
        .def_readwrite("cmd_id", &him_pkg::SimpleLowCmd::cmd_id)
        .def_readwrite("input_state_id", &him_pkg::SimpleLowCmd::input_state_id)
        .def_readwrite("ts_infer_input_state", &him_pkg::SimpleLowCmd::ts_infer_input_state)
        .def_readwrite("ts_before_infer", &him_pkg::SimpleLowCmd::ts_before_infer)
        .def_readwrite("ts_after_infer", &him_pkg::SimpleLowCmd::ts_after_infer);
    
    // 绑定LowCmdPublisher类
    py::class_<him_pkg::LowCmdPublisher>(m, "LowCmdPublisher")
        .def(py::init<const std::string&>(), 
             py::arg("topic_name") = "/rt/lowcmd",
             "Initialize LowCmd publisher")
        .def("start", &him_pkg::LowCmdPublisher::start,
             "Start the publisher")
        .def("stop", &him_pkg::LowCmdPublisher::stop,
             "Stop the publisher")
        .def("is_running", &him_pkg::LowCmdPublisher::is_running,
             "Check if publisher is running")
        .def("publish_message", &him_pkg::LowCmdPublisher::publish_message,
             py::arg("cmd_data"),
             "Publish a LowCmd message");
    
    // 便利函数：从Python列表/numpy数组创建SimpleLowCmd
    m.def("create_simple_lowcmd", [](
        py::array_t<float> motor_q,
        py::array_t<float> motor_dq, 
        py::array_t<float> motor_kp,
        py::array_t<float> motor_kd,
        py::array_t<float> motor_tau,
        uint16_t cmd_id,
        uint16_t input_state_id,
        uint64_t ts_infer_input_state,
        uint64_t ts_before_infer,
        uint64_t ts_after_infer
    ) -> him_pkg::SimpleLowCmd {
        him_pkg::SimpleLowCmd cmd;
        
        // 检查数组大小
        if (motor_q.size() != 12 || motor_dq.size() != 12 || 
            motor_kp.size() != 12 || motor_kd.size() != 12 || motor_tau.size() != 12) {
            throw std::runtime_error("All motor arrays must have exactly 12 elements");
        }
        
        // 复制数据
        auto q_ptr = motor_q.unchecked<1>();
        auto dq_ptr = motor_dq.unchecked<1>();
        auto kp_ptr = motor_kp.unchecked<1>();
        auto kd_ptr = motor_kd.unchecked<1>();
        auto tau_ptr = motor_tau.unchecked<1>();
        
        for (size_t i = 0; i < 12; ++i) {
            cmd.motor_q[i] = q_ptr(i);
            cmd.motor_dq[i] = dq_ptr(i);
            cmd.motor_kp[i] = kp_ptr(i);
            cmd.motor_kd[i] = kd_ptr(i);
            cmd.motor_tau[i] = tau_ptr(i);
        }
        
        cmd.cmd_id = cmd_id;
        cmd.input_state_id = input_state_id;
        cmd.ts_infer_input_state = ts_infer_input_state;
        cmd.ts_before_infer = ts_before_infer;
        cmd.ts_after_infer = ts_after_infer;
        
        return cmd;
    }, "Create SimpleLowCmd from numpy arrays",
       py::arg("motor_q"), py::arg("motor_dq"), py::arg("motor_kp"), 
       py::arg("motor_kd"), py::arg("motor_tau"), py::arg("cmd_id"),
       py::arg("input_state_id"), py::arg("ts_infer_input_state"),
       py::arg("ts_before_infer"), py::arg("ts_after_infer"));
} 