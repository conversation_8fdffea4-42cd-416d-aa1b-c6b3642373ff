#include "him_pkg/lowcmd_publisher.hpp"
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

namespace him_pkg {

// 静态变量来跟踪ROS2初始化状态
static std::atomic<bool> ros2_initialized_by_us{false};
static std::mutex ros2_init_mutex;

LowCmdPublisher::LowCmdPublisher(const std::string& topic_name)
    : topic_name_(topic_name), running_(false) {
    
    std::lock_guard<std::mutex> lock(ros2_init_mutex);
    // 如果ROS2没有初始化，就初始化它
    if (!rclcpp::ok()) {
        rclcpp::init(0, nullptr);
        ros2_initialized_by_us.store(true);
    }
    
    try {
        // 创建节点，使用默认上下文
        node_ = rclcpp::Node::make_shared("lowcmd_publisher_cpp");
        
        // 创建执行器
        executor_ = std::make_shared<rclcpp::executors::SingleThreadedExecutor>();
        executor_->add_node(node_);
        
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("Failed to create ROS2 node: ") + e.what());
    }
}

LowCmdPublisher::~LowCmdPublisher() {
    stop();
    
    // 清理执行器和节点的循环引用
    if (executor_ && node_) {
        try {
            executor_->remove_node(node_);
        } catch (const std::exception& e) {
            // 忽略移除节点时的错误，确保清理过程继续
        }
    }
    
    // 清理shared_ptr
    executor_.reset();
    node_.reset();
    
    // 如果是我们初始化的ROS2，则关闭它
    std::lock_guard<std::mutex> lock(ros2_init_mutex);
    if (ros2_initialized_by_us.load()) {
        if (rclcpp::ok()) {
            rclcpp::shutdown();
        }
        ros2_initialized_by_us.store(false);
    }
}

void LowCmdPublisher::start() {
    if (running_.load()) {
        return;
    }
    
    if (!rclcpp::ok()) {
        throw std::runtime_error("ROS2 is not running! Cannot start publisher.");
    }
    
    try {
        // 创建发布者
        publisher_ = node_->create_publisher<lowlevel_msg::msg::LowCmd>(
            topic_name_,
            1  // QoS depth
        );
        
        running_.store(true);
        
        // 在单独线程中启动spin
        spin_thread_ = std::thread([this]() {
            while (running_.load() && rclcpp::ok()) {
                try {
                    // 使用较长的超时时间减少CPU使用
                    executor_->spin_once(std::chrono::milliseconds(10));
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(node_->get_logger(), "Error in spin_once: %s", e.what());
                    break;
                }
            }
        });
        
    } catch (const std::exception& e) {
        running_.store(false);
        throw std::runtime_error(std::string("Failed to start publisher: ") + e.what());
    }
}

void LowCmdPublisher::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    
    // 等待spin线程结束
    if (spin_thread_.joinable()) {
        spin_thread_.join();
    }
    
    // 清理发布者
    if (publisher_) {
        publisher_.reset();
    }
}

bool LowCmdPublisher::is_running() const {
    return running_.load();
}

void LowCmdPublisher::publish_message(const SimpleLowCmd& cmd_data) {
    if (!running_.load() || !publisher_) {
        throw std::runtime_error("Publisher is not running!");
    }
    
    std::lock_guard<std::mutex> lock(publish_mutex_);
    
    try {
        // 转换为完整的ROS2消息
        auto ros_msg = convert_to_ros_message(cmd_data);
        
        // 发布消息
        publisher_->publish(*ros_msg);
        
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node_->get_logger(), "Error publishing message: %s", e.what());
        throw;
    }
}

lowlevel_msg::msg::LowCmd::SharedPtr LowCmdPublisher::convert_to_ros_message(const SimpleLowCmd& cmd_data) {
    auto msg = std::make_shared<lowlevel_msg::msg::LowCmd>();
    
    // 初始化消息头部
    msg->head[0] = 0xFE;
    msg->head[1] = 0xEF;
    msg->level_flag = 0xFF;
    msg->frame_reserve = 0;
    
    // 设置序列号和版本
    msg->sn[0] = 0;
    msg->sn[1] = 0;
    msg->version[0] = 0;
    msg->version[1] = 0;
    
    msg->bandwidth = 0;
    
    // 设置电机命令（只设置前12个电机）
    for (size_t i = 0; i < 12 && i < msg->motor_cmd.size(); ++i) {
        msg->motor_cmd[i].mode = 0x0A;  // 位置控制模式
        msg->motor_cmd[i].q = cmd_data.motor_q[i];
        msg->motor_cmd[i].dq = cmd_data.motor_dq[i];
        msg->motor_cmd[i].tau = cmd_data.motor_tau[i];
        msg->motor_cmd[i].kp = cmd_data.motor_kp[i];
        msg->motor_cmd[i].kd = cmd_data.motor_kd[i];
        
        // 清零保留字段
        for (size_t j = 0; j < 3; ++j) {
            msg->motor_cmd[i].reserve[j] = 0;
        }
    }
    
    // 清零剩余的电机命令
    for (size_t i = 12; i < msg->motor_cmd.size(); ++i) {
        msg->motor_cmd[i].mode = 0;
        msg->motor_cmd[i].q = 0.0f;
        msg->motor_cmd[i].dq = 0.0f;
        msg->motor_cmd[i].tau = 0.0f;
        msg->motor_cmd[i].kp = 0.0f;
        msg->motor_cmd[i].kd = 0.0f;
        for (size_t j = 0; j < 3; ++j) {
            msg->motor_cmd[i].reserve[j] = 0;
        }
    }
    
    // 设置BMS命令
    msg->bms_cmd.off = 0;
    for (size_t i = 0; i < 3; ++i) {
        msg->bms_cmd.reserve[i] = 0;
    }
    
    // 清零其他字段
    for (size_t i = 0; i < 40; ++i) {
        msg->wireless_remote[i] = 0;
    }
    for (size_t i = 0; i < 12; ++i) {
        msg->led[i] = 0;
    }
    for (size_t i = 0; i < 2; ++i) {
        msg->fan[i] = 0;
    }
    msg->gpio = 0;
    msg->reserve = 0;
    msg->crc = 0;
    
    // 设置控制ID和时间戳
    msg->cmd_id = cmd_data.cmd_id;
    msg->input_state_id = cmd_data.input_state_id;
    msg->ts_infer_input_state = cmd_data.ts_infer_input_state;
    msg->ts_before_infer = cmd_data.ts_before_infer;
    msg->ts_after_infer = cmd_data.ts_after_infer;
    
    return msg;
}

} // namespace him_pkg 