from setuptools import find_packages, setup
import os
from glob import glob

package_name = 'him_pkg'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('lib/python3.10/site-packages/' + package_name, ['him_pkg/libmodel_task.so']),
    ] + [
        (
            f'share/{package_name}/runs/gait-conditioned-agility/exported/{os.path.basename(folder)}',
            glob(f'him_pkg/runs/gait-conditioned-agility/exported/{os.path.basename(folder)}/*')
        )
        for folder in glob('him_pkg/runs/gait-conditioned-agility/exported/*') if os.path.isdir(folder)
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='siyu.zhou',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'him_pkg_mt = him_pkg.deploy_policy_ros_multithread:main',
        ],
    },
)
