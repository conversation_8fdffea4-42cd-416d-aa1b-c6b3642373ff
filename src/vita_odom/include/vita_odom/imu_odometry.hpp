// Copyright 2025 VitaDynamics Limited

#pragma once

#include <array>
#include <memory>
#include <string>

// ROS2
#include <tf2_ros/transform_broadcaster.h>

#include <Eigen/Dense>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <rclcpp/rclcpp.hpp>

// 根据VITA_MSG宏选择不同的消息包
#ifdef VITA_MSG
#include <lowlevel_msg/msg/imu_debug.hpp>
#include <lowlevel_msg/msg/low_state.hpp>
using LowStateMsg = lowlevel_msg::msg::LowState;
#else
#include <unitree_go/msg/low_state.hpp>
using LowStateMsg = unitree_go::msg::LowState;
#endif

#include "vita_odom/lowlevel_state.hpp"
#include "vita_odom/robot_model.hpp"
#include "vita_odom/unitree_estimator.hpp"
#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

class FeetContactForces;

/**
 * @brief 基于IMU的里程计节点，使用Unitree LowState消息
 */
class ImuOdometry : public rclcpp::Node {
 public:
  /**
   * @brief 构造函数
   */
  explicit ImuOdometry();

  /**
   * @brief 析构函数
   */
  ~ImuOdometry();

 private:
  /**
   * @brief LowState数据回调函数
   * @param msg LowState消息
   */
  void LowStateCallback(const typename LowStateMsg::SharedPtr msg);

  /**
   * @brief 发布里程计数据
   */
  void PublishOdometry(const rclcpp::Time &current_time);

  /**
   * @brief 更新足部接触状态
   * @param foot_forces 足部力传感器数据数组 [FR, FL, RR, RL]
   */
  void UpdateFootContactState(const std::array<int16_t, 4> &foot_forces);

  /**
   * @brief 计算足部接触状态
   */
  void CalcFootContactState();

  // ROS2节点相关
  typename rclcpp::Subscription<LowStateMsg>::SharedPtr low_state_sub_;
  rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
#ifdef VITA_MSG
  rclcpp::Publisher<lowlevel_msg::msg::IMUDebug>::SharedPtr imu_debug_pub_;
#endif
  std::shared_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

  // 参数
  std::string world_frame_id_;
  std::string base_frame_id_;

  // 协方差估计参数
  bool enable_covariance_estimation_;

  // 里程计数据
  nav_msgs::msg::Odometry odom_msg_;
  geometry_msgs::msg::TransformStamped odom_tf_;

  // 协方差数组
  std::array<double, 36> pose_covariance_;
  std::array<double, 36> twist_covariance_;

  // 初始姿态相关
  bool is_first_imu_data_;
  Eigen::Quaterniond initial_quat_;

  // 时间跟踪
  rclcpp::Time last_msg_time_;

  // Unitree状态估计器相关
  std::unique_ptr<QuadrupedRobot> robot_model_;             // 机器人模型
  std::unique_ptr<LowlevelState> low_state_;                // 低级别状态
  VecInt4 contact_state_;                                   // 接触状态
  Vec4 phase_;                                              // 相位
  std::unique_ptr<Estimator> estimator_;                    // 状态估计器
  std::unique_ptr<FeetContactForces> feet_contact_forces_;  // 足部接触力计算器
};

}  // namespace vita_odom
