// Copyright 2025 VitaDynamics Limited

#pragma once

#include <limits>
#include <stdexcept>
#include <string>
#include <vector>

// Eigen
#include <Eigen/Dense>

namespace vita_odom {

/**
 * @brief 协方差均值计算类
 */
class AvgCov {
 public:
  /**
   * @brief 构造函数
   * @param dim 维度
   * @param name 名称
   */
  AvgCov(int dim, const std::string &name = "") : dim_(dim), name_(name) {
    current_mean_ = Eigen::MatrixXd::Zero(dim, dim);
    count_ = 0;
  }

  /**
   * @brief 添加新的协方差矩阵
   * @param new_cov 新的协方差矩阵
   */
  void addCov(const Eigen::MatrixXd &new_cov) {
    if (count_ == 0) {
      current_mean_ = new_cov;
    } else {
      current_mean_ = (current_mean_ * count_ + new_cov) / (count_ + 1);
    }
    ++count_;
  }

  /**
   * @brief 获取当前均值
   * @return 均值协方差矩阵
   */
  const Eigen::MatrixXd &getMean() const { return current_mean_; }

  /**
   * @brief 获取维度
   * @return 维度
   */
  int getDim() const { return dim_; }

  /**
   * @brief 获取采样数量
   * @return 采样数量
   */
  int getCount() const { return count_; }

  /**
   * @brief 获取名称
   * @return 名称
   */
  std::string getName() const { return name_; }

 private:
  int dim_;                       // 维度
  std::string name_;              // 名称
  Eigen::MatrixXd current_mean_;  // 当前均值
  int count_;                     // 采样数量
};

/**
 * @brief 低通滤波器
 */
class LPFilter {
 public:
  /**
   * @brief 构造函数
   * @param dt 时间步长
   * @param cutoff 截止频率
   */
  LPFilter(double dt, double cutoff) : cutoff_(cutoff) {
    alpha_ = dt / (dt + 1.0 / (2.0 * M_PI * cutoff_));
    value_ = 0.0;
  }

  /**
   * @brief 添加新值
   * @param new_value 新值
   * @return 滤波后的值
   */
  double addValue(double new_value) {
    value_ = alpha_ * new_value + (1.0 - alpha_) * value_;
    return value_;
  }

  /**
   * @brief 获取当前值
   * @return 当前值
   */
  double getValue() const { return value_; }

  /**
   * @brief 设置新的时间步长
   * @param dt 新的时间步长
   */
  void setDt(double dt) { alpha_ = dt / (dt + 1.0 / (2.0 * M_PI * cutoff_)); }

 private:
  double alpha_;   // 滤波器参数
  double value_;   // 当前值
  double cutoff_;  // 截止频率
};

/**
 * @brief 协方差估计类 - 用于计算数据的均值和完整协方差矩阵（包括协方差）
 *
 * 该类能够在线计算输入数据的统计特性，包括：
 * - 样本均值向量
 * - 完整的协方差矩阵（包含对角线方差和非对角线协方差）
 * - 支持增量更新，适用于实时估计
 */
class CovEst {
 public:
  /**
   * @brief 构造函数
   * @param dim 数据维度
   * @param wait_samples 开始统计前的等待样本数，默认100
   */
  CovEst(int dim, int wait_samples = 100)
      : dim_(dim), wait_samples_(wait_samples) {
    // 初始化存储矩阵
    mean_ = Eigen::VectorXd::Zero(dim);
    covariance_ = Eigen::MatrixXd::Zero(dim, dim);

    // 统计计数器
    total_count_ = 0;
    valid_count_ = 0;
    // 验证输入参数
    if (dim <= 0) {
      throw std::invalid_argument("CovEst: dimension must be positive");
    }
    if (wait_samples < 0) {
      wait_samples_ = 0;
    }
  }

  /**
   * @brief 添加新的数据样本进行统计
   * @param new_sample 新的数据样本向量
   * @return true 如果样本被成功处理，false 如果样本被丢弃（等待期或维度错误）
   */
  bool addSample(const Eigen::VectorXd &new_sample) {
    // 验证输入维度
    if (new_sample.size() != dim_) {
      return false;
    }

    ++total_count_;

    // 等待期：收集足够样本后再开始统计
    if (total_count_ <= wait_samples_) {
      return false;
    }

    ++valid_count_;

    // 增量更新均值和协方差
    updateStatistics(new_sample);

    return true;
  }

  /**
   * @brief 获取当前估计的均值向量
   * @return 均值向量的常量引用
   */
  const Eigen::VectorXd &getMean() const { return mean_; }

  /**
   * @brief 获取当前估计的协方差矩阵
   * @return 协方差矩阵的常量引用
   */
  const Eigen::MatrixXd &getCovariance() const { return covariance_; }

  /**
   * @brief 获取有效样本数量
   * @return 参与统计计算的样本数量
   */
  int getValidCount() const { return valid_count_; }

  /**
   * @brief 获取总样本数量
   * @return 包括等待期在内的总样本数
   */
  int getTotalCount() const { return total_count_; }

  /**
   * @brief 获取数据维度
   * @return 数据维度
   */
  int getDim() const { return dim_; }

  /**
   * @brief 重置统计状态
   */
  void reset() {
    mean_.setZero();
    covariance_.setZero();
    total_count_ = 0;
    valid_count_ = 0;
  }

  /**
   * @brief 检查是否有足够的样本进行可靠估计
   * @param min_samples 最小所需样本数，默认为数据维度的2倍
   * @return true 如果样本数足够，false 否则
   */
  bool isReliable(int min_samples = -1) const {
    if (min_samples < 0) {
      min_samples = std::max(2 * dim_, 10);  // 默认至少需要2*dim个样本
    }
    return valid_count_ >= min_samples;
  }

  /**
   * @brief 获取协方差矩阵的特征值（用于检查数值稳定性）
   * @return 特征值向量，按降序排列
   */
  Eigen::VectorXd getEigenvalues() const {
    Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> solver(covariance_);
    return solver.eigenvalues().reverse();  // 降序排列
  }

  /**
   * @brief 获取协方差矩阵的条件数（数值稳定性指标）
   * @return 条件数，如果矩阵奇异则返回无穷大
   */
  double getConditionNumber() const {
    Eigen::VectorXd eigenvals = getEigenvalues();
    double max_eigenval = eigenvals(0);
    double min_eigenval = eigenvals(eigenvals.size() - 1);
    if (std::abs(min_eigenval) < 1e-12) {
      return std::numeric_limits<double>::infinity();
    }
    return max_eigenval / min_eigenval;
  }

 private:
  /**
   * @brief 使用增量算法更新均值和协方差矩阵
   * @param new_sample 新样本
   *
   * 算法基于 Welford's online algorithm:
   * - mean_new = mean_old + (sample - mean_old) / n
   * - cov_new = cov_old*(n-1)/n + (sample - mean_old)*(sample - mean_new)^T/n
   */
  void updateStatistics(const Eigen::VectorXd &new_sample) {
    const double n = static_cast<double>(valid_count_);

    // 保存更新前的均值
    Eigen::VectorXd mean_old = mean_;
    // 更新均值: μ_n = μ_{n-1} + (x_n - μ_{n-1}) / n
    mean_ += (new_sample - mean_) / n;
    // 更新协方差矩阵: 使用增量公式避免数值不稳定
    if (valid_count_ == 1) {
      // 第一个样本：协方差为零
      covariance_.setZero();
    } else {
      // Welford算法更新协方差
      Eigen::VectorXd delta_old = new_sample - mean_old;
      Eigen::VectorXd delta_new = new_sample - mean_;
      covariance_ =
          covariance_ * (n - 1.0) / n + (delta_old * delta_new.transpose()) / n;
    }
  }

  // 核心参数
  int dim_;           // 数据维度
  int wait_samples_;  // 等待样本数

  // 统计数据
  Eigen::VectorXd mean_;        // 当前均值估计
  Eigen::MatrixXd covariance_;  // 当前协方差矩阵估计

  // 计数器
  int total_count_;  // 总样本数
  int valid_count_;  // 有效样本数（参与统计的样本）
};
}  // namespace vita_odom
