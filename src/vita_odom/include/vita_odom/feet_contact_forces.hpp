// Copyright 2025 VitaDynamics Limited

#pragma once

#include <Eigen/Dense>

#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

class QuadrupedRobot;
class LowlevelState;
/**
 * @brief 足部接触力计算类
 *
 * 根据机器人状态和足部雅可比矩阵计算足部地面反作用力(GRF)
 */
class FeetContactForces {
public:
  /**
   * @brief 构造函数
   * @param robot_model 机器人模型(提供足部雅可比矩阵等)
   */
  explicit FeetContactForces(QuadrupedRobot *robot_model);

  /**
   * @brief 获取所有足部地面反作用力
   *
   * @param state 机器人低层状态
   * @param feet_grf
   * 输出参数，计算得到的所有足部地面反作用力，顺序是FR、FL、RR、RL
   * @return 成功计算的足部数量
   */
  int getAllFeetGRF(const LowlevelState &state, Vec34 &feet_grf);

private:
  QuadrupedRobot *robot_model_;
};

} // namespace vita_odom