// Copyright 2025 VitaDynamics Limited

#pragma once

#include <memory>
#include <string>

// ROS2
#include <rclcpp/rclcpp.hpp>

#include "vita_odom/avg_cov.hpp"
#include "vita_odom/lowlevel_state.hpp"
#include "vita_odom/robot_model.hpp"
#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

// Fixed-size matrix types for better SIMD optimization
using Mat28 = Eigen::Matrix<double, 28, 28>;
using Mat28_18 = Eigen::Matrix<double, 28, 18>;
using Mat18_28 = Eigen::Matrix<double, 18, 28>;
using Vec28 = Eigen::Matrix<double, 28, 1>;

/**
 * @brief 状态估计器类
 */
class Estimator {
 EIGEN_MAKE_ALIGNED_OPERATOR_NEW  // Ensure proper memory alignment for SIMD

     public :
     /**
      * @brief 构造函数
      * @param robot_model 机器人模型
      * @param low_state 低级别状态
      * @param contact 接触状态
      * @param phase 相位
      * @param dt 时间步长
      * @param Qdig 过程噪声对角线
      * @param est_name 估计器名称
      * @param logger ROS logger
      */
     Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
               VecInt4 *contact, Vec4 *phase, double dt, Vec18 Qdig,
               std::string est_name,
               rclcpp::Logger logger = rclcpp::get_logger("estimator"));

  /**
   * @brief 构造函数(使用默认参数)
   * @param robot_model 机器人模型
   * @param low_state 低级别状态
   * @param contact 接触状态
   * @param phase 相位
   * @param dt 时间步长
   * @param logger ROS logger
   */
  Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
            VecInt4 *contact, Vec4 *phase, double dt,
            rclcpp::Logger logger = rclcpp::get_logger("estimator"));

  /**
   * @brief 析构函数
   */
  ~Estimator();

  /**
   * @brief 初始化系统
   */
  void initSystem();

  /**
   * @brief 运行估计器
   */
  void run();

  /**
   * @brief 重置状态估计器
   * 将位置和速度重置为零，保持其他状态不变
   */
  void reset();

  /**
   * @brief 获取位置
   * @return 位置向量
   */
  Vec3 getPosition();

  /**
   * @brief 获取速度
   * @return 速度向量
   */
  Vec3 getVelocity();

  /**
   * @brief 获取足端位置
   * @param i 腿ID
   * @return 足端位置
   */
  Vec3 getFootPos(int i);

  /**
   * @brief 获取所有足端位置
   * @return 足端位置矩阵
   */
  Vec34 getFeetPos();

  /**
   * @brief 获取所有足端速度
   * @return 足端速度矩阵
   */
  Vec34 getFeetVel();

  /**
   * @brief 获取足端相对于机身的全局位置
   * @return 足端位置矩阵
   */
  Vec34 getPosFeet2BGlobal();

  /**
   * @brief 设置时间步长
   * @param dt 新的时间步长
   */
  void setDt(double dt);

  /**
   * @brief 获取测量噪声协方差估计器 (用于调试和参数调优)
   * @return RCheck_协方差估计器的指针
   */
  const CovEst *getRCheck() const { return RCheck_; }

  /**
   * @brief 获取控制输入协方差估计器 (用于调试和参数调优)
   * @return uCheck_协方差估计器的指针
   */
  const CovEst *getUCheck() const { return uCheck_; }

  /**
   * @brief 设置协方差估计是否启用
   * @param enable 是否启用协方差估计
   */
  void setEnableCovarianceEstimation(bool enable) {
    enable_covariance_estimation_ = enable;
  }

  /**
   * @brief 获取协方差估计是否启用
   * @return 协方差估计是否启用
   */
  bool isEnableCovarianceEstimation() const {
    return enable_covariance_estimation_;
  }

 private:
  /**
   * @brief 从YAML配置文件加载估计器参数
   */
  void loadEstimatorConfig();

  QuadrupedRobot *robot_model_;  // 机器人模型
  LowlevelState *low_state_;     // 低级别状态
  VecInt4 *contact_;             // 接触状态
  Vec4 *phase_;                  // 相位
  double dt_;                    // 时间步长
  Vec18 Qdig_;                   // 过程噪声对角线
  std::string est_name_;         // 估计器名称

  Vec3 g_;                 // 重力向量
  double large_variance_;  // 大方差值

  // 状态估计相关变量 - 使用固定大小矩阵
  Vec18 xhat_;                      // 状态估计
  Vec3 u_;                          // 控制输入
  Vec3 acc_offset_;                 // 加速度偏移量
  double acc_scale_;                // 加速度scale因子
  Mat18 A_;                         // 状态转移矩阵
  Eigen::Matrix<double, 18, 3> B_;  // 控制输入矩阵
  Mat28_18 C_;                      // 测量矩阵
  Mat18 P_;                         // 协方差矩阵
  Mat18 Q_;                         // 过程噪声矩阵
  Eigen::MatrixXd R_;               // 测量噪声矩阵
  Eigen::MatrixXd RInit_;           // 初始测量噪声矩阵
  Mat18 QInit_;                     // 初始过程噪声矩阵
  Mat3 Cu_;                         // 控制噪声相关

  // 滤波器相关变量 - 恢复动态矩阵类型确保正确性
  Mat18 Ppriori_;                             // 先验协方差
  Eigen::MatrixXd S_;                         // 系统创新协方差
  Eigen::PartialPivLU<Eigen::MatrixXd> Slu_;  // LU分解
  Eigen::VectorXd Sy_;                        // 系统创新
  Eigen::MatrixXd Sc_;                        // 系统创新
  Eigen::MatrixXd SR_;                        // 系统创新
  Eigen::MatrixXd STC_;                       // 系统创新
  Mat18 IKC_;                                 // 状态估计更新

  // 足部相关变量
  Vec4 feetH_;               // 足部高度
  Vec12 feetPos2Body_;       // 足部位置
  Vec12 feetVel2Body_;       // 足部速度
  Vec34 feetPosGlobalKine_;  // 足部位置全局运动学
  Vec34 feetVelGlobalKine_;  // 足部速度全局运动学

  Mat3 rotMatB2G_;  // 从体坐标系到全局坐标系的旋转矩阵

  double trust_;  // 信任度

  // 协方差检查
  CovEst *RCheck_;    // 测量噪声协方差估计
  CovEst *uCheck_;    // 控制输入协方差估计
  CovEst *accCheck_;  // 加速度偏移量协方差估计

  // 速度滤波器
  LPFilter *vxFilter_;  // X方向速度滤波器
  LPFilter *vyFilter_;  // Y方向速度滤波器
  LPFilter *vzFilter_;  // Z方向速度滤波器

  // 测量 - 恢复动态向量类型
  Eigen::VectorXd y_;     // 测量
  Eigen::VectorXd yhat_;  // 预测测量

  // 协方差估计控制
  bool enable_covariance_estimation_;  // 协方差估计是否启用
  int covariance_counter_;             // 协方差输出计数器

  // ROS logger
  rclcpp::Logger logger_;  // ROS logger用于日志输出
};

/**
 * @brief Go2状态估计器类
 */
class Go2Estimator : public Estimator {
 EIGEN_MAKE_ALIGNED_OPERATOR_NEW  // Ensure proper memory alignment

     public :
     /**
      * @brief 构造函数
      * @param low_state 低级别状态
      * @param contact 接触状态
      * @param phase 相位
      * @param dt 时间步长
      * @param logger ROS logger
      */
     Go2Estimator(LowlevelState *low_state, VecInt4 *contact, Vec4 *phase,
                  double dt,
                  rclcpp::Logger logger = rclcpp::get_logger("go2_estimator"));
};

/**
 * @brief Vita00状态估计器类
 */
class Vita00Estimator : public Estimator {
 EIGEN_MAKE_ALIGNED_OPERATOR_NEW  // Ensure proper memory alignment

     public :
     /**
      * @brief 构造函数
      * @param low_state 低级别状态
      * @param contact 接触状态
      * @param phase 相位
      * @param dt 时间步长
      * @param logger ROS logger
      */
     Vita00Estimator(
         LowlevelState *low_state, VecInt4 *contact, Vec4 *phase, double dt,
         rclcpp::Logger logger = rclcpp::get_logger("vita00_estimator"));
};

/**
 * @brief Vita01状态估计器类
 */
class Vita01Estimator : public Estimator {
 EIGEN_MAKE_ALIGNED_OPERATOR_NEW  // Ensure proper memory alignment

     public :
     /**
      * @brief 构造函数
      * @param low_state 低级别状态
      * @param contact 接触状态
      * @param phase 相位
      * @param dt 时间步长
      * @param logger ROS logger
      */
     Vita01Estimator(
         LowlevelState *low_state, VecInt4 *contact, Vec4 *phase, double dt,
         rclcpp::Logger logger = rclcpp::get_logger("vita01_estimator"));
};

}  // namespace vita_odom
