// Copyright 2025 VitaDynamics Limited

#include "vita_odom/feet_contact_forces.hpp"

#include <cmath>

#include "vita_odom/lowlevel_state.hpp"
#include "vita_odom/robot_model.hpp"

namespace {
constexpr double kGravity = 9.81; // 重力加速度
} // namespace

namespace vita_odom {

bool getFootGRFbywholeleg(QuadrupedRobot *robot, const LowlevelState &state,
                          int leg, Vec3 &foot_grf) {
  Vec3 tau_leg = state.getTau().col(leg);
  Mat3 foot_jacobian = robot->getJaco(state, leg);
  if (std::abs(foot_jacobian.determinant()) < 1e-6) {
    printf("Foot Jacobian Matrix is singular for leg %d\n", leg);
    return false;
  }

  // 计算重力分量在机身坐标系中的表示
  Mat3 R = state.getRotMat();
  Vec3 gravity_world(0, 0, -kGravity);
  Vec3 gravity_body = R * gravity_world;

  // 考虑机身加速度和角加速度的影响
  Vec3 foot_pos = robot->getFootPosition(state, leg, FrameType::BODY);

  // 足部的加速度包括以下分量：
  // 1. 线性加速度
  // 2. 由角加速度引起的切向加速度
  // 3. 由角速度引起的离心加速度
  Vec3 omega = state.imu.getGyro();   // 角速度
  Vec3 foot_acc = state.imu.getAcc(); // 基础加速度

  // 添加离心加速度的影响: a_centrifugal = omega × (omega × r)
  foot_acc += omega.cross(omega.cross(foot_pos));

  // 获取腿部质量参数，使用实际从URDF提取的质量
  float leg_mass = robot->getLegMass(leg);

  // 计算惯性力: F_inertia = m * a
  Vec3 inertia_force = leg_mass * foot_acc;

  // 计算重力补偿: F_gravity = m * g
  Vec3 gravity_force = leg_mass * gravity_body;

  // 使用实际的关节力矩进行更准确的计算
  // 基于公式：F = (J^T)^(-1) * (tau - h)
  // 其中 h 是包含重力、科里奥利力和离心力的项

  // 动力学模型中的h项估计：
  Vec3 h_term = foot_jacobian.transpose() * (gravity_force + inertia_force);

  // 使用直接逆矩阵计算GRF
  foot_grf = -(foot_jacobian.transpose().inverse()) * (tau_leg - h_term);

  // 检查计算结果是否有效
  return foot_grf.allFinite();
}

bool getFootGRFbyknee(QuadrupedRobot *robot, const LowlevelState &state,
                      int leg, Vec3 &foot_grf) {
  // 1. 获取关节力矩和角度
  Vec3 tau_leg = state.getTau().col(leg);
  Vec3 q_leg = state.getQ().col(leg);

  // 2. 获取机器人参数
  float calf_length = robot->getKneeLinkLength(leg);
  float calf_mass = robot->getCalfMass(leg);

  // 3. 计算重力补偿
  Mat3 R = state.getRotMat();
  Vec3 gravity_world(0, 0, -kGravity);
  Vec3 gravity_body = R * gravity_world;

  // 4. 计算重力对小腿的影响
  float gravity_effect = calf_mass * gravity_body(2);

  // 5. 计算有效力矩（考虑重力影响）
  float effective_tau = tau_leg(2) - gravity_effect * calf_length * 0.5;

  // 6. 计算力臂（考虑实际几何关系）
  float force_arm = calf_length * cos(q_leg(1));

  // 7. 计算垂直方向的力
  float vertical_force = effective_tau / force_arm;

  // 8. 设置GRF
  foot_grf.setZero();
  foot_grf(2) = vertical_force;

  return true;
}

FeetContactForces::FeetContactForces(QuadrupedRobot *robot_model)
    : robot_model_(robot_model) {}

int FeetContactForces::getAllFeetGRF(const LowlevelState &state,
                                     Vec34 &feet_grf) {
  int success_count = 0;

  // 为四条腿分别计算GRF
  for (int leg = 0; leg < 4; leg++) {
    Vec3 leg_grf;
    // 计算腿部GRF
    bool success = false;
    success = getFootGRFbyknee(robot_model_, state, leg, leg_grf);
    if (success) {
      feet_grf.col(leg) = leg_grf;
      success_count++;
    } else {
      // 如果计算失败，设置为零向量
      feet_grf.col(leg).setZero();
    }
  }

  return success_count;
}

} // namespace vita_odom
