cmake_minimum_required(VERSION 3.8)
project(vita_odom)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 定义宏选项，默认启用VITA_MSG
option(VITA_MSG "Use lowlevel_msg instead of unitree_go" ON)

 #定义宏选项，默认启用USE_KNEE_GRF
option(USE_KNEE_GRF "Use knee GRF" ON)

# Architecture-specific SIMD optimization
if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|AMD64")
  message(STATUS "Detected x86_64 architecture - enabling AVX/AVX2/AVX512 optimizations")
  
  # Check for AVX512 support
  include(CheckCXXCompilerFlag)
  check_cxx_compiler_flag("-mavx512f" COMPILER_SUPPORTS_AVX512)
  check_cxx_compiler_flag("-mavx2" COMPILER_SUPPORTS_AVX2)
  check_cxx_compiler_flag("-mfma" COMPILER_SUPPORTS_FMA)
  
  if(COMPILER_SUPPORTS_AVX512)
    set(SIMD_FLAGS "-mavx512f -mavx512dq -mavx512bw -mavx512vl")
    add_compile_definitions(EIGEN_VECTORIZE_AVX512)
    message(STATUS "Using AVX512 optimization")
  elseif(COMPILER_SUPPORTS_AVX2)
    set(SIMD_FLAGS "-mavx2")
    add_compile_definitions(EIGEN_VECTORIZE_AVX2)
    message(STATUS "Using AVX2 optimization")
  else()
    set(SIMD_FLAGS "-msse4.2")
    add_compile_definitions(EIGEN_VECTORIZE_SSE4_2)
    message(STATUS "Using SSE4.2 optimization")
  endif()
  
  if(COMPILER_SUPPORTS_FMA)
    set(SIMD_FLAGS "${SIMD_FLAGS} -mfma")
    message(STATUS "FMA instructions enabled")
  endif()
  
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
  message(STATUS "Detected ARM64 architecture - enabling NEON optimizations")
  set(SIMD_FLAGS "-march=native")
  add_compile_definitions(EIGEN_VECTORIZE_NEON)
  
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
  message(STATUS "Detected ARM32 architecture - enabling NEON optimizations")
  set(SIMD_FLAGS "-mfpu=neon")
  add_compile_definitions(EIGEN_VECTORIZE_NEON)
  
else()
  message(STATUS "Unknown architecture - using generic optimizations")
  set(SIMD_FLAGS "-march=native")
endif()

# Enable OpenMP for Eigen threading
find_package(OpenMP)
if(OpenMP_CXX_FOUND)
  set(THREADING_FLAGS "-fopenmp")
  add_compile_definitions(EIGEN_USE_THREADS)
  message(STATUS "OpenMP found - enabling Eigen threading")
else()
  set(THREADING_FLAGS "")
  message(STATUS "OpenMP not found - single-threaded mode")
endif()

# Combine optimization flags
set(OPTIMIZATION_FLAGS "-O3 -DNDEBUG -funroll-loops -ffast-math")
set(PERF_FLAGS "${OPTIMIZATION_FLAGS};${SIMD_FLAGS};${THREADING_FLAGS}")

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
  # Add performance flags separately to avoid parsing issues
  separate_arguments(PERF_FLAGS_LIST UNIX_COMMAND "${PERF_FLAGS}")
  add_compile_options(${PERF_FLAGS_LIST})
endif()

# Enable Eigen optimizations
add_compile_definitions(
  EIGEN_NO_DEBUG
  EIGEN_FAST_MATH=1
)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(yaml-cpp REQUIRED)

# 根据宏选项选择依赖包
if(VITA_MSG)
  find_package(lowlevel_msg REQUIRED)
  add_compile_definitions(VITA_MSG)
  message(STATUS "Using lowlevel_msg package")
  set(MSG_DEPENDENCY lowlevel_msg)
else()
  find_package(unitree_go REQUIRED)
  message(STATUS "Using unitree_go package")
  set(MSG_DEPENDENCY unitree_go)
endif()

message(STATUS "Eigen3_INCLUDE_DIR: ${EIGEN3_INCLUDE_DIR}")
message(STATUS "Performance flags: ${PERF_FLAGS}")

# 使用绝对路径确保clangd能找到头文件
include_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${EIGEN3_INCLUDE_DIR}
)

# 创建IMU Odometry节点
add_executable(imu_odometry_node
  src/imu_odometry_node.cpp
  src/imu_odometry.cpp
  src/unitree_estimator.cpp
  src/feet_contact_forces.cpp
  src/system_utils.cpp
)

# 为目标添加更明确的包含目录
target_include_directories(imu_odometry_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

ament_target_dependencies(imu_odometry_node
  rclcpp
  sensor_msgs
  nav_msgs
  geometry_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  Eigen3
  ${MSG_DEPENDENCY}
)

# Link OpenMP if available
if(OpenMP_CXX_FOUND)
  target_link_libraries(imu_odometry_node OpenMP::OpenMP_CXX)
endif()

# Link yaml-cpp
target_link_libraries(imu_odometry_node yaml-cpp)

# 安装目标
install(TARGETS
  imu_odometry_node
  DESTINATION lib/${PROJECT_NAME}
)

# 安装头文件
install(DIRECTORY
  include/
  DESTINATION include
)

# 安装启动脚本
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}
)

ament_package()