from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument


def generate_launch_description():
    """生成启动IMU里程计节点的启动描述。

    配置参数：
        world_frame_id: 世界坐标系ID, 默认为 vita_odom
        base_frame_id: 机器人基座坐标系ID, 默认为 base_link_imu
        input_topic: 输入LowState消息的话题名, 默认为/rt/lowstate
        output_topic: 输出Odometry消息的话题名, 默认为/rt/odom
        enable_covariance_estimation: 是否启用协方差估计, 默认为 false
    """

    # 声明参数
    world_frame_id = LaunchConfiguration("world_frame_id")
    base_frame_id = LaunchConfiguration("base_frame_id")
    input_topic = LaunchConfiguration("input_topic")
    output_topic = LaunchConfiguration("output_topic")
    enable_covariance_estimation = LaunchConfiguration("enable_covariance_estimation")

    return LaunchDescription(
        [
            # 参数定义
            DeclareLaunchArgument(
                "world_frame_id",
                default_value="vita_odom",
                description="世界坐标系ID",
            ),
            DeclareLaunchArgument(
                "base_frame_id",
                default_value="base_link_imu",
                description="机器人基座坐标系ID",
            ),
            DeclareLaunchArgument(
                "input_topic",
                default_value="/rt/lowstate",
                description="输入话题名 (LowState消息)",
            ),
            DeclareLaunchArgument(
                "output_topic",
                default_value="/rt/odom",
                description="输出话题名 (Odometry消息)",
            ),
            DeclareLaunchArgument(
                "enable_covariance_estimation",
                default_value="false",
                description="是否启用协方差估计 (用于传感器标定)",
            ),
            # 启动IMU里程计节点
            Node(
                package="vita_odom",
                executable="imu_odometry_node",
                name="imu_odometry",
                parameters=[
                    {
                        "world_frame_id": world_frame_id,
                        "base_frame_id": base_frame_id,
                        "enable_covariance_estimation": enable_covariance_estimation,
                    }
                ],
                remappings=[("/lowstate", input_topic), ("/rt/odom", output_topic)],
                output="log",
            ),
        ]
    )
