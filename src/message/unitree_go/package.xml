<?xml version="1.0"?>
<?xml-model href=""?>
<package format="3">
  <name>unitree_go</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">Unitree</maintainer>
<license>BSD 3-Clause License</license>
  
  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>
  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>geometry_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
