uint8[2] head
uint8 level_flag
uint8 frame_reserve
uint32[2] sn
uint32[2] version
uint16 bandwidth
IMUState imu_state
MotorCmd[20] relative_motor_cmd
MotorState[20] motor_state
int16[4] foot_force
int16[4] foot_force_est
uint32 tick
uint8[40] wireless_remote
uint8 bit_flag
float32 adc_reel
int8 temperature_ntc1
int8 temperature_ntc2
uint16[4] fan_frequency
uint32 reserve
uint32 crc
uint16 state_id
uint16 input_cmd_id
uint64 ts_input_cmd_recv
uint64 ts_input_cmd_into_motor
uint64 ts_state_real
uint64 ts_state_pub