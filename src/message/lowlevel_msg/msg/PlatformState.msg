string soc_uid

# S100 / X5
string platform_type

float32[] cpu_temperature
float32[] bpu_temperature
float32[] mcu_temperature
float32[] bpu_usage
float32 cpu_us
float32 cpu_sy
float32 cpu_ni
float32 cpu_id
float32 cpu_wa
float32 cpu_hi
float32 cpu_si
float32 cpu_st
float32 mem_total
float32 mem_free
float32 mem_used
float32 mem_buffcache
uint32 ddr_freq

# Top 20 CPU consuming processes
string[] process_names
float32[] process_cpu_usage
float32[] process_mem_usage