uint8 version_high
uint8 version_low
uint8 version_patch
string software_version

# bit0: charging mos
# bit1: discharging mos
# bit2: pre-charging mos
# bit3: pre-discharging mos
# 0 for mos close, 1 for mos open
uint8 status

# bit0 单体过压保护  bit5 充电低温保护     bit10 短路保护
# bit1 单体欠压保护  bit6 放电过温保护     bit11 前端检测 IC
# bit2 整组过压保护  bit7 放电低温保护     bit12 软件锁定
# bit3 整组欠压保护  bit8 充电过流保护错误
uint16 error_code
uint32 vendor_alarm

uint16 voltage              # 总电压 (mV)
int32 current               # 电流 (mA)
uint16 soc                  # 剩余电量百分比 (0-100)
uint16 soh                  # 健康状态百分比 (0-100)

# cycle count
uint16 cycle

# 电池基本信息
uint16 nominal_capacity      # 10mAh
uint16 remaining_capacity    # 10mAh
uint8 count_ntc             # 温度传感器数量
uint8 count_serial          # 串联电池数量
uint16 manufacturer_date    # 生产日期
string serial_number        # 序列号

# 系统状态
bool shutdown               # 是否需要关机

# 温度数据 (原始uint16_t格式)
uint16[] temperatures

# 单体电压数据 (mV)
uint16[] cell_voltages

# 原始温度数据 (uint16_t格式)
uint16[] bq_ntc