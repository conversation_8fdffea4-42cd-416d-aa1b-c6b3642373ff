<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>vita_flow</name>
  <version>0.0.1</version>
  <description>ROS2 data flow management package for recording and playing back data</description>
  <maintainer email="<EMAIL>">pezy</maintainer>
  <license>Apache-2.0</license>

  <depend>rclpy</depend>
  <depend>sim_msg</depend>
  <depend>rosbag2_py</depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
