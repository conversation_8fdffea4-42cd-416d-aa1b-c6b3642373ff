#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sim_msg.srv import Control
from rosbag2_py import SequentialReader, StorageOptions, ConverterOptions
from rclpy.qos import QoSProfile
import time
import threading
from rclpy.serialization import deserialize_message
from rosidl_runtime_py.utilities import get_message
from rclpy.publisher import Publisher
from typing import Dict, Optional
import asyncio
import sys


class PlayerNode(Node):
    # 在类级别声明属性
    _topic_publishers: Dict[str, Publisher]
    _reader: Optional[SequentialReader]
    _playing: bool
    _play_thread: Optional[threading.Thread]
    _recorder_client: rclpy.client.Client

    def __init__(self):
        super().__init__("play")

        # 初始化属性
        self._topic_publishers = {}
        self._reader = None
        self._playing = False
        self._play_thread = None

        # 声明参数
        self.declare_parameters(
            namespace="",
            parameters=[
                ("rate", 1.0),  # 回放速率
                (
                    "topics",
                    "/sim/lowcmd,/rl_lowcmd,/rt/lowstate",
                ),  # 要回放的话题列表，字符串形式
            ],
        )

        self._recorder_client = self.create_client(Control, "control_recording")
        self.get_logger().info("Player node has been initialized")

    async def start_recording(self) -> bool:
        # 等待 recorder 服务可用
        while not self._recorder_client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info("Waiting for recorder service...")

        # 发送开始录制请求
        request = Control.Request()
        request.command = "start"
        response = await self._recorder_client.call_async(request)

        if response.success:
            self.get_logger().info("Recording started successfully")
        else:
            self.get_logger().error("Failed to start recording")

        return response.success

    async def stop_recording(self) -> bool:
        request = Control.Request()
        request.command = "stop"
        response = await self._recorder_client.call_async(request)

        if response.success:
            self.get_logger().info("Recording stopped successfully")
        else:
            self.get_logger().error("Failed to stop recording")

        return response.success

    def create_publisher_for_topic(self, topic_name: str, topic_type: str) -> bool:
        """为指定话题创建发布器"""
        try:
            msg_type = get_message(topic_type)
            self._topic_publishers[topic_name] = self.create_publisher(
                msg_type, topic_name, QoSProfile(depth=10)
            )
            self.get_logger().info(f"Created publisher for topic: {topic_name}")
            return True
        except Exception as e:
            self.get_logger().error(
                f"Failed to create publisher for topic {topic_name}: {str(e)}"
            )
            return False

    def play_messages(self):
        try:
            # 获取要回放的话题列表
            target_topics = set(self.get_parameter("topics").value.split(","))
            self.get_logger().info(f"Target topics to play: {target_topics}")

            # 获取包中的所有话题信息
            topic_types = self._reader.get_all_topics_and_types()
            self.get_logger().info(
                f"Available topics in bag: {[t.name for t in topic_types]}"
            )

            # 为目标话题创建发布器
            for topic_info in topic_types:
                if topic_info.name in target_topics:
                    self.get_logger().info(
                        f"Creating publisher for topic: {topic_info.name} ({topic_info.type})"
                    )
                    self.create_publisher_for_topic(topic_info.name, topic_info.type)

            # 如果没有找到任何目标话题，记录警告
            if not self._topic_publishers:
                self.get_logger().warn(
                    "None of the specified topics were found in the bag file"
                )
                self._playing = False
                return

            self.get_logger().info("Starting message playback loop")
            rate = self.get_parameter("rate").value
            self.get_logger().info(f"Playback rate: {rate}x")

            # 初始化时间控制变量
            first_msg = True
            start_time = None
            bag_start_time = None

            while rclpy.ok() and self._playing:
                try:
                    if not self._reader.has_next():
                        self.get_logger().info("No more messages in bag file")
                        self._playing = False
                        break

                    # 读取消息
                    topic_name, msg_data, msg_time = self._reader.read_next()

                    # 初始化时间
                    if first_msg:
                        first_msg = False
                        bag_start_time = msg_time
                        start_time = time.time()
                        self.get_logger().info(
                            "First message received, starting playback"
                        )
                    else:
                        # 计算应该等待的时间
                        bag_time_elapsed = (msg_time - bag_start_time) / 1e9  # 转换为秒
                        real_time_elapsed = time.time() - start_time

                        # 计算需要等待的时间
                        wait_time = (bag_time_elapsed / rate) - real_time_elapsed

                        if wait_time > 0:
                            time.sleep(wait_time)

                    # 只处理目标话题
                    if topic_name in self._topic_publishers:
                        try:
                            # 获取消息类型并反序列化
                            msg_type = get_message(
                                next(
                                    info.type
                                    for info in topic_types
                                    if info.name == topic_name
                                )
                            )
                            msg = deserialize_message(msg_data, msg_type)

                            # 发布消息
                            self._topic_publishers[topic_name].publish(msg)
                            self.get_logger().debug(
                                f"Published message on topic: {topic_name}"
                            )
                        except Exception as e:
                            self.get_logger().error(
                                f"Error publishing message on topic {topic_name}: {str(e)}"
                            )

                except Exception as e:
                    self.get_logger().error(f"Error in playback loop: {str(e)}")
                    self._playing = False
                    break

            self.get_logger().info("Playback loop ended")

        except Exception as e:
            self.get_logger().error(f"Error during playback: {str(e)}")
            self._playing = False

    async def play_bag(self, bag_path: str) -> bool:
        try:
            self.get_logger().info(f"Attempting to play bag from path: {bag_path}")

            if not bag_path:
                self.get_logger().error("No bag path specified")
                return False

            # 开始录制
            self.get_logger().info("Starting recording...")
            if not await self.start_recording():
                return False

            # 配置回放选项
            self.get_logger().info("Configuring storage options...")
            storage_options = StorageOptions(uri=bag_path, storage_id="mcap")
            converter_options = ConverterOptions(
                input_serialization_format="cdr", output_serialization_format="cdr"
            )

            # 初始化回放器
            self.get_logger().info("Initializing reader...")
            self._reader = SequentialReader()
            self._reader.open(storage_options, converter_options)
            self.get_logger().info("Reader initialized successfully")

            # 开始回放
            self._playing = True
            self.get_logger().info("Starting playback thread...")
            self._play_thread = threading.Thread(target=self.play_messages)
            self._play_thread.start()
            self.get_logger().info("Playback thread started")

            # 等待回放完成
            while self._playing and rclpy.ok():
                await asyncio.sleep(0.1)  # 使用 asyncio.sleep 替代 rclpy.sleep

            # 停止录制
            await self.stop_recording()

            return True

        except Exception as e:
            self.get_logger().error(f"Failed to play bag: {str(e)}")
            await self.stop_recording()
            return False
        finally:
            if self._play_thread:
                self._play_thread.join()
            self._playing = False


def main(args=None):
    # 初始化 ROS2，但保留原始参数
    rclpy.init(args=args)

    # 获取命令行参数（跳过 ros2 run vita_flow play 部分）
    if len(sys.argv) < 2:
        print("Usage: ros2 run vita_flow play <bag_path>")
        return

    # 获取最后一个参数作为 bag_path
    bag_path = sys.argv[-1]

    try:
        node = PlayerNode()

        # 创建执行器并运行
        executor = rclpy.executors.SingleThreadedExecutor()
        executor.add_node(node)

        # 开始执行
        executor_thread = threading.Thread(target=executor.spin)
        executor_thread.start()

        # 运行主要的播放逻辑
        asyncio.run(node.play_bag(bag_path))
    except KeyboardInterrupt:
        pass
    finally:
        executor.shutdown()
        if "executor_thread" in locals():
            executor_thread.join()
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
