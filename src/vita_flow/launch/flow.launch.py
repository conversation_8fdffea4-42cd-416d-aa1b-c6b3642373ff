from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import (
    DeclareLaunchArgument,
    ExecuteProcess,
)
from launch.substitutions import (
    LaunchConfiguration,
    FindExecutable,
)
import os


def get_env_or_default(name, default):
    return os.environ.get(name, default)


def generate_launch_description():
    bag_path_arg = DeclareLaunchArgument(
        "bag_path", description="Path to the bag file to play"
    )

    storage_path = get_env_or_default("VITA_STORAGE_PATH", "/mnt/ramdisk/")
    recorder_topics = get_env_or_default(
        "VITA_RECORDER_TOPICS", "/sim/lowcmd,/sim/lowstate,/rl_lowcmd,/rt/lowstate"
    )
    player_topics = get_env_or_default(
        "VITA_PLAYER_TOPICS", "/sim/lowcmd,/rl_lowcmd,/rt/lowstate"
    )
    player_rate = get_env_or_default("VITA_PLAYER_RATE", "1.0")

    # 创建 recorder 节点
    recorder_node = Node(
        package="vita_flow",
        executable="record",
        name="record",
        parameters=[
            {
                "storage_path": storage_path,
                "topics": recorder_topics,
            }
        ],
    )

    # 创建 player 节点 - 不再使用TimerAction延迟
    player_node = ExecuteProcess(
        cmd=[
            FindExecutable(name="ros2"),
            "run",
            "vita_flow",
            "play",
            "--ros-args",
            "-p",
            f"topics:={player_topics}",
            "-p",
            f"rate:={player_rate}",
            "--",
            LaunchConfiguration("bag_path"),
        ],
        name="play",
        output="screen",
    )

    return LaunchDescription(
        [
            bag_path_arg,
            recorder_node,
            player_node,
        ]
    )
