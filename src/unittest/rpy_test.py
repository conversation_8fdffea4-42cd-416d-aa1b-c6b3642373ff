import numpy as np
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd
import time
import threading

# 初始状态的关节位置（从joint_limit_test.py复制）
initial_joint_pos = np.array(
    [-0.161, 1.19, -2.76, 0.161, 1.19, -2.76, -0.161, 1.19, -2.76, 0.161, 1.19, -2.76],
    dtype=float,
)

# 站立姿态的关节位置 (FR, FL, RR, RL)
standing_pos = np.array(
    [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5], dtype=float
)

# 控制参数
dt = 0.002  # 2ms发送一次


def generate_rpy_pose(roll, pitch, yaw, standing_pos):
    """
    根据目标RPY角度计算关节位置
    roll, pitch, yaw: 目标角度（弧度）
    standing_pos: 12维站立q
    返回: 12维目标动作q

    关节顺序：FR(0,1,2), <PERSON>(3,4,5), RR(6,7,8), RL(9,10,11)
    每组内顺序：髋关节, 大腿关节, 小腿关节

    坐标系：右手坐标系，X前Y左Z上
    Roll: 绕X轴旋转，负值为左低右高（左倾），正值为右低左高（右倾）
    Pitch: 绕Y轴旋转，正值为前高后低
    Yaw: 绕Z轴旋转，正值为逆时针（从上往下看）

    髋关节运动：正值=腿向上抬，负值=腿向下压
    """
    q = standing_pos.copy()

    # ------- Roll分配 -------
    # 负roll值实现左低右高（左倾），正roll值实现右低左高（右倾）
    # 髋关节微调：倾斜时适当调整站姿稳定性
    # 髋关节：正值=向下摆，负值=向上摆
    q[0] += roll  # FR 髋
    q[3] += roll  # FL 髋
    q[6] += roll  # RR 髋
    q[9] += roll  # RL 髋

    # 大腿和小腿配合调整高度差（主要实现Roll效果）
    q[4] -= roll  # FL 大腿
    q[5] += roll  # FL 小腿
    q[10] -= roll  # RL 大腿
    q[11] += roll  # RL 小腿
    q[1] += roll  # FR 大腿
    q[2] -= roll  # FR 小腿
    q[7] += roll  # RR 大腿
    q[8] -= roll  # RR 小腿

    # ------- Pitch分配 -------
    # 绕Y轴旋转，正pitch值实现前低后高
    q[1] += pitch  # FR 大腿
    q[2] -= pitch  # FR 小腿
    q[4] += pitch  # FL 大腿
    q[5] -= pitch  # FL 小腿
    q[7] -= pitch  # RR 大腿
    q[8] += pitch  # RR 小腿
    q[10] -= pitch  # RL 大腿
    q[11] += pitch  # RL 小腿

    # ------- Yaw分配 -------
    # 绕Z轴旋转，通过对角腿的髋关节调整模拟转向
    # 正yaw值为逆时针转向（从上往下看）
    q[0] -= yaw  # FR 髋 (向外)
    q[3] -= yaw  # FL 髋 (向外)
    q[6] += yaw  # RR 髋 (向内)
    q[9] += yaw  # RL 髋 (向内)

    # 大腿和小腿配合实现扭转效果
    # 前腿：髋关节外展后，右侧收缩，左侧拉伸（前部右倾）
    q[1] -= yaw  # FR 大腿 (收缩，降低)
    q[2] += yaw  # FR 小腿 (收缩，更弯曲)
    q[4] += yaw  # FL 大腿 (拉伸，抬高)
    q[5] -= yaw  # FL 小腿 (拉伸，更伸展)

    # 后腿：髋关节内收后，左侧收缩，右侧拉伸（后部左倾）
    q[10] -= yaw  # RL 大腿 (收缩，降低)
    q[11] += yaw  # RL 小腿 (收缩，更弯曲)
    q[7] += yaw  # RR 大腿 (拉伸，抬高)
    q[8] -= yaw  # RR 小腿 (拉伸，更伸展)

    return q


# RPY测试关键帧序列
rpy_test_positions = [
    # 1. 从初始位置到站立位置
    {
        "name": "Stand up from initial position",
        "pos": standing_pos.copy(),
        "duration": 3.0,
        "use_static_pos": True,  # 使用静态位置
    },
    # 2. Roll测试 - 向左倾斜 - 0.175 rad
    {
        "name": "Roll left (0.175 rad)",
        "rpy": (-0.175, 0.0, 0.0),
        "base_pos": "standing",  # 基于站立位置
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 3. Roll测试 - 向右倾斜 - 0.175 rad
    {
        "name": "Roll right (0.175 rad)",
        "rpy": (0.175, 0.0, 0.0),
        "base_pos": "standing",  # 基于站立位置
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 4. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 5. Pitch测试 - 前倾 - 0.32 rad
    {
        "name": "Pitch forward (0.32 rad)",
        "rpy": (0.0, 0.32, 0.0),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 6. Pitch测试 - 后倾 - 0.2 rad
    {
        "name": "Pitch backward (-0.2 rad)",
        "rpy": (0.0, -0.2, 0.0),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 7. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 8. Yaw测试 - 向左转 - 0.14 rad
    {
        "name": "Yaw left (0.14 rad)",
        "rpy": (0.0, 0.0, 0.14),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 9. Yaw测试 - 向右转 - 0.14 rad
    {
        "name": "Yaw right (-0.14 rad)",
        "rpy": (0.0, 0.0, -0.14),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 10. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 11. 组合测试 - Roll + Pitch
    {
        "name": "Combined: Roll right + Pitch forward",
        "rpy": (0.1, 0.2, 0.0),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 12. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 13. 组合测试 - Roll + Yaw
    {
        "name": "Combined: Roll left + Yaw left",
        "rpy": (-0.1, 0.0, 0.1),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 14. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 15. 组合测试 - Pitch + Yaw
    {
        "name": "Combined: Pitch backward + Yaw right",
        "rpy": (0.0, -0.1, -0.1),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 16. 回到站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
    # 17. 全组合测试
    {
        "name": "Combined: Roll + Pitch + Yaw",
        "rpy": (0.1, 0.1, 0.1),
        "base_pos": "standing",
        "duration": 2.0,
        "use_static_pos": False,
    },
    # 18. 最终站立姿态
    {
        "name": "Final standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
        "use_static_pos": True,
    },
]


class RPYTest(Node):
    def __init__(self, mode="SIM"):
        super().__init__("rpy_test")

        # 设置运行模式
        self.mode = mode.upper()
        if self.mode not in ["SIM", "REAL"]:
            raise ValueError(f"Invalid mode: {mode}. Must be 'SIM' or 'REAL'")

        self.test_positions = rpy_test_positions
        self.test_name = f"RPY Keyframe Test ({self.mode} Mode)"

        # 根据模式选择topic
        topic = "/sim/lowcmd" if self.mode == "SIM" else "/rl_lowcmd"
        self.pub = self.create_publisher(LowCmd, topic, 1)
        self.timer = self.create_timer(dt, self.timer_callback)

        # 初始化命令消息
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF

        # 初始化电机命令（从初始位置开始）
        for i in range(12):
            motor_cmd = MotorCmd()
            motor_cmd.mode = 0x01
            motor_cmd.q = initial_joint_pos[i]
            motor_cmd.kp = 0.0
            motor_cmd.dq = 0.0
            motor_cmd.kd = 0.0
            motor_cmd.tau = 0.0
            self.cmd.motor_cmd.append(motor_cmd)

        # 测试状态
        self.current_test_index = 0
        self.current_pos = initial_joint_pos.copy()
        self.target_pos = initial_joint_pos.copy()
        self.start_pos = initial_joint_pos.copy()
        self.step_count = 0
        self.total_steps = 0
        self.finished = False
        self.waiting_for_user = False

        self.get_logger().info(f"{self.test_name} initialized")
        self.get_logger().info(f"Total tests: {len(self.test_positions)}")

    def start_next_test(self):
        if self.current_test_index >= len(self.test_positions):
            self.get_logger().info("All RPY tests completed!")
            self.finished = True
            threading.Thread(target=self._shutdown).start()
            return

        test = self.test_positions[self.current_test_index]
        self.start_pos = self.current_pos.copy()

        # 根据测试类型计算目标位置
        if test.get("use_static_pos", False):
            # 使用预定义的静态位置
            self.target_pos = test["pos"].copy()
        else:
            # 动态计算RPY位置
            roll, pitch, yaw = test["rpy"]
            base_pos_type = test["base_pos"]

            if base_pos_type == "standing":
                base_pos = standing_pos
            elif base_pos_type == "current":
                base_pos = self.current_pos
            else:
                # 默认使用站立位置
                base_pos = standing_pos

            self.target_pos = generate_rpy_pose(roll, pitch, yaw, base_pos)

        self.total_steps = int(test["duration"] / dt)
        self.step_count = 0

        self.get_logger().info(
            f"Starting test {self.current_test_index + 1}/{len(self.test_positions)}: {test['name']}"
        )
        self.get_logger().info(
            f"Duration: {test['duration']}s ({self.total_steps} steps)"
        )

    def timer_callback(self):
        if self.finished or self.waiting_for_user:
            return

        # 如果是第一次运行或当前测试完成，开始下一个测试
        if self.step_count == 0 and self.total_steps == 0:
            self.start_next_test()
            if self.finished:
                return

        if self.step_count < self.total_steps:
            # 计算当前步骤的插值比例
            ratio = self.step_count / self.total_steps

            # 线性插值计算当前关节位置
            self.current_pos = (1 - ratio) * self.start_pos + ratio * self.target_pos

            # 更新电机命令
            for i in range(12):
                self.cmd.motor_cmd[i].q = self.current_pos[i]
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].tau = 0.0

                # 根据模式和关节类型设置kp/kd参数
                # 小腿关节索引：2, 5, 8, 11 (每组的第3个关节)
                is_calf_joint = i % 3 == 2

                if self.mode == "REAL" and is_calf_joint:
                    # REAL模式下小腿关节的kp/kd要除以4（减速比原因）
                    self.cmd.motor_cmd[i].kp = 30.0 / 4.0
                    self.cmd.motor_cmd[i].kd = 1.0 / 4.0
                else:
                    # SIM模式或非小腿关节使用标准参数
                    self.cmd.motor_cmd[i].kp = 30.0
                    self.cmd.motor_cmd[i].kd = 1.0

            # 发布命令
            self.pub.publish(self.cmd)

            # 更新步骤计数
            self.step_count += 1

            # 每500步打印一次进度
            if self.step_count % 500 == 0:
                progress = (self.step_count / self.total_steps) * 100
                self.get_logger().info(
                    f"Test {self.current_test_index + 1} progress: {progress:.1f}%"
                )

        else:
            # 当前步骤完成
            test = self.test_positions[self.current_test_index]
            test_name = test["name"]
            self.current_pos = self.target_pos.copy()
            self.get_logger().info(f"Completed: {test_name}")

            # 等待用户确认继续
            self.waiting_for_user = True
            threading.Thread(target=self._wait_for_user_input).start()

            # 直接继续下一个测试
            # self.current_test_index += 1
            # self.step_count = 0
            # self.total_steps = 0

    def _wait_for_user_input(self):
        """等待用户输入继续"""
        try:
            if self.current_test_index < len(self.test_positions) - 1:
                input(f"\nPress Enter to continue to next test...")
            else:
                input(f"\nPress Enter to finish all tests...")
        except KeyboardInterrupt:
            self.get_logger().info("Test interrupted by user.")
            self.finished = True
            threading.Thread(target=self._shutdown).start()
            return

        # 继续下一个步骤
        self.current_test_index += 1
        self.step_count = 0
        self.total_steps = 0
        self.waiting_for_user = False

    def _shutdown(self):
        time.sleep(1.0)
        rclpy.shutdown()


def main(args=None):
    print("RPY Keyframe Test")
    print("=================")
    print("Available modes:")
    print("  1. SIM mode (Simulation)")
    print("  2. REAL mode (Real robot)")
    print()

    while True:
        try:
            choice = input("Please select mode (1 or 2): ").strip()
            if choice == "1":
                mode = "SIM"
                break
            elif choice == "2":
                mode = "REAL"
                break
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except KeyboardInterrupt:
            print("\nTest cancelled.")
            return

    print(f"\n{mode} Mode Selected")
    print("=" * (len(mode) + 13))

    if mode == "SIM":
        print("Topic: /sim/lowcmd")
        print("Parameters: Standard kp/kd for all joints")
    else:
        print("Topic: /rl_lowcmd")
        print("Parameters: Reduced kp/kd for calf joints (÷4 for gear ratio)")

    print("\nThe robot will test RPY motions with the following keyframes:")
    print()
    for i, test in enumerate(rpy_test_positions):
        print(f"  {i+1}. {test['name']} ({test['duration']}s)")
    print()
    print("After each keyframe, the program will pause and wait for your confirmation.")
    print("Press Ctrl+C to stop the test at any time.")
    print("\nPress Enter to start the RPY keyframe test...")
    input()

    rclpy.init(args=args)
    node = RPYTest(mode)

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    finally:
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == "__main__":
    main()
