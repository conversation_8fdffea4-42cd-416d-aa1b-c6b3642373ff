#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import time
import argparse
import signal
import sys

class VelocityCommander(Node):
    def __init__(self, cmd_vel=0.0, cmd_yaw=0.0, duration=5.0):
        super().__init__('velocity_commander')
        
        self.cmd_vel = cmd_vel
        self.cmd_yaw = cmd_yaw
        self.duration = duration
        self.start_time = None
        self.is_running = False
        
        # 发布到 /script_cmd 话题，由 arbitrator 处理
        self.publisher = self.create_publisher(
            Twist,
            '/script_cmd',
            10
        )
        
        # 50Hz 发布频率，确保命令持续发送
        self.timer = self.create_timer(0.02, self.timer_callback)
        
        self.get_logger().info(f"VelocityCommander initialized:")
        self.get_logger().info(f"  Linear velocity: {self.cmd_vel} m/s")
        self.get_logger().info(f"  Angular velocity: {self.cmd_yaw} rad/s")
        self.get_logger().info(f"  Duration: {self.duration} seconds")
        self.get_logger().info(f"  Publishing to: /script_cmd")
        
    def start_command(self):
        """开始发送命令"""
        self.start_time = time.time()
        self.is_running = True
        self.get_logger().info("Started sending velocity commands")
        
    def timer_callback(self):
        """定时器回调，持续发送命令"""
        if not self.is_running:
            return
            
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        if elapsed_time >= self.duration:
            self.stop_command()
            return
            
        # 创建并发布 Twist 消息
        msg = Twist()
        msg.linear.x = self.cmd_vel
        msg.linear.y = 0.0
        msg.linear.z = 0.0
        msg.angular.x = 0.0
        msg.angular.y = 0.0
        msg.angular.z = self.cmd_yaw
        
        self.publisher.publish(msg)
        
        # 每秒打印一次进度
        if int(elapsed_time) != int(elapsed_time - 0.02):
            remaining = self.duration - elapsed_time
            self.get_logger().info(f"Sending commands... {remaining:.1f}s remaining")
        
    def stop_command(self):
        """停止发送命令"""
        if self.is_running:
            self.is_running = False
            # 发送零速度命令
            msg = Twist()
            self.publisher.publish(msg)
            self.get_logger().info("Stopped sending velocity commands")
            
            # 延迟一点时间确保零速度命令被发送
            time.sleep(0.1)
            
            # 退出程序
            rclpy.shutdown()

def main():
    parser = argparse.ArgumentParser(description='Send velocity commands for a specified duration')
    parser.add_argument('--velocity', type=float, default=0.5, 
                       help='Linear velocity in m/s (default: 0.5)')
    parser.add_argument('--yaw', type=float, default=0.0,
                       help='Angular velocity in rad/s (default: 0.0)')
    parser.add_argument('--duration', type=float, default=5.0,
                       help='Duration in seconds (default: 5.0)')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.duration <= 0:
        print("Error: Duration must be positive")
        sys.exit(1)
    
    rclpy.init()
    
    try:
        commander = VelocityCommander(args.velocity, args.yaw, args.duration)
        
        def signal_handler(sig, frame):
            print("\nReceived interrupt signal, stopping...")
            commander.stop_command()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 开始发送命令
        commander.start_command()
        
        # 运行节点
        rclpy.spin(commander)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()

if __name__ == '__main__':
    main()
