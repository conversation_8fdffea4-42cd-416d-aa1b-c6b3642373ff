import time
import sys
import numpy as np
import rclpy
from rclpy.node import Node
from unitree_go.msg import LowCmd, MotorCmd

stand_up_joint_pos = np.array([
    0.00571868, 0.608813, -1.21763, -0.00571868, 0.608813, -1.21763,
    0.00571868, 0.608813, -1.21763, -0.00571868, 0.608813, -1.21763
],
                              dtype=float)

stand_down_joint_pos = np.array([
    0.0473455, 1.22187, -2.44375, -0.0473455, 1.22187, -2.44375, 0.0473455,
    1.22187, -2.44375, -0.0473455, 1.22187, -2.44375
],
                                dtype=float)

dt = 0.002
runing_time = 0.0

class StandControl(Node):
    def __init__(self):
        super().__init__('stand_control')
        
        # Create a publisher for the LowCmd message
        self.pub = self.create_publisher(LowCmd, '/sim/lowcmd', 1)
        
        # Create a timer for 500Hz control
        self.timer = self.create_timer(dt, self.timer_callback)
        
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF
        self.cmd.gpio = 0
        
        # Initialize motor commands
        self.cmd.motor_cmd = [MotorCmd() for _ in range(20)]
        for motor in self.cmd.motor_cmd:
            motor.mode = 0x01  # (PMSM) mode
            motor.q = 0.0
            motor.kp = 0.0
            motor.dq = 0.0
            motor.kd = 0.0
            motor.tau = 0.0
            
        self.runing_time = 0.0

    def timer_callback(self):
        self.runing_time += dt

        if (self.runing_time < 3.0):
            # Stand up in first 3 second
            phase = np.tanh(self.runing_time / 1.2)
            for i in range(12):
                self.cmd.motor_cmd[i].q = phase * stand_up_joint_pos[i] + (
                    1 - phase) * stand_down_joint_pos[i]
                self.cmd.motor_cmd[i].kp = phase * 50.0 + (1 - phase) * 20.0
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 3.5
                self.cmd.motor_cmd[i].tau = 0.0
        else:
            # Then stand down
            phase = np.tanh((self.runing_time - 3.0) / 1.2)
            for i in range(12):
                self.cmd.motor_cmd[i].q = phase * stand_down_joint_pos[i] + (
                    1 - phase) * stand_up_joint_pos[i]
                self.cmd.motor_cmd[i].kp = 50.0
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 3.5
                self.cmd.motor_cmd[i].tau = 0.0

        self.pub.publish(self.cmd)

def main(args=None):
    input("Press enter to start")
    
    rclpy.init(args=args)
    node = StandControl()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
