// Copyright 2025 VitaDynamics Limited

#include <cstdio>
#include <memory>
#include <string>

#include <backward.hpp>
#include <rclcpp/rclcpp.hpp>

#include "vita_sim/config.h"
#include "vita_sim/info.h"
#include "vita_sim/mj_utils/mj_env.h"

// 设置 backward 信号处理
backward::SignalHandling sh;

int main(int argc, char **argv) {
  rclcpp::init(argc, argv);

  vita_sim::SimConfig config = vita_sim::LoadConfig("config.yaml");

  mujoco::Simulator sim;
  sim.SetModelCallback([](mujoco::Simulate &sim) {
    vita_sim::PrintSceneInformation(sim);
    vita_sim::AutoSelectBody(sim);
  });
  sim.Start(config);

  rclcpp::shutdown();
  return 0;
}
