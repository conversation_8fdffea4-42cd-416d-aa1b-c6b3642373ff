cmake_minimum_required(VERSION 3.16)
project(vita_sim)

set(CMAKE_POLICY_DEFAULT_CMP0048 NEW)
cmake_policy(SET CMP0048 NEW)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

if(APPLE)
  message(STATUS "Enable Objective-C and Objective-C++")
  enable_language(OBJC)
  enable_language(OBJCXX)
endif()

# 调试
# set(CMAKE_BUILD_TYPE Debug)
# set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")

# 添加 FetchContent 模块
include(FetchContent)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_index_cpp REQUIRED)
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)

if(APPLE)
  message(STATUS "Searching for system-installed mujoco on macOS")
  find_package(mujoco QUIET)

  if(mujoco_FOUND)
    message(STATUS "Found system-installed mujoco")
  else()
    message(STATUS "System mujoco not found, use `mamba install mujoco=3.3.1 -c conda-forge` to install mujoco")
  endif()

  if(NOT TARGET glfw)
    message(STATUS "Fetching GLFW 3.3.8 for macOS build")
    FetchContent_Declare(
      glfw
      GIT_REPOSITORY https://github.com/glfw/glfw.git
      GIT_TAG 7482de6071d21db77a7236155da44c172a7f6c9e
    )

    # 配置 GLFW 编译选项
    set(GLFW_BUILD_DOCS OFF CACHE BOOL "")
    set(GLFW_BUILD_TESTS OFF CACHE BOOL "")
    set(GLFW_BUILD_EXAMPLES OFF CACHE BOOL "")
    set(GLFW_INSTALL OFF CACHE BOOL "")
    set(BUILD_SHARED_LIBS OFF CACHE BOOL "")

    FetchContent_MakeAvailable(glfw)
  endif()

  if(NOT TARGET lodepng)
    message(STATUS "Fetching lodepng for macOS build")
    FetchContent_Declare(
      lodepng
      GIT_REPOSITORY https://github.com/lvandeve/lodepng.git
      GIT_TAG b4ed2cd7ecf61d29076169b49199371456d4f90b
    )

    FetchContent_GetProperties(lodepng)

    if(NOT lodepng_POPULATED)
      FetchContent_Populate(lodepng)

      # 创建 lodepng 静态库
      add_library(lodepng STATIC ${lodepng_SOURCE_DIR}/lodepng.cpp)
      target_include_directories(lodepng PUBLIC ${lodepng_SOURCE_DIR})
      set_target_properties(lodepng PROPERTIES POSITION_INDEPENDENT_CODE ON)
    endif()
  endif()
else()
  # 其他环境：使用 FetchContent 方式
  if(NOT TARGET mujoco)
    message(STATUS "Fetching mujoco 3.3.0")
    FetchContent_Declare(
      mujoco
      GIT_REPOSITORY https://github.com/google-deepmind/mujoco.git
      GIT_TAG 3.3.0
      CMAKE_ARGS
      -DMUJOCO_BUILD_EXAMPLES=OFF
      -DMUJOCO_BUILD_TESTS=OFF
    )
    FetchContent_MakeAvailable(mujoco)
  endif()
endif()

# 添加 backward 库
if(NOT TARGET backward)
  message(STATUS "Fetching backward")
  FetchContent_Declare(
    backward
    GIT_REPOSITORY https://github.com/bombela/backward-cpp.git
    GIT_TAG v1.6
  )

  FetchContent_GetProperties(backward)

  if(NOT backward_POPULATED)
    FetchContent_Populate(backward)

    # 在这里设置 CMAKE_POLICY_VERSION_MINIMUM
    set(CMAKE_POLICY_VERSION_MINIMUM 3.5)

    # 添加子目录
    add_subdirectory(${backward_SOURCE_DIR} ${backward_BINARY_DIR})
  endif()
endif()

# 添加选项以选择使用哪个消息包
option(USE_UNITREE_GO "Use unitree_go messages instead of lowlevel_msg" OFF)

if(USE_UNITREE_GO)
  add_definitions(-DUSE_UNITREE_GO)
  find_package(unitree_go REQUIRED)
  set(MSG_DEPS unitree_go)
else()
  find_package(lowlevel_msg REQUIRED)
  set(MSG_DEPS lowlevel_msg)
endif()

# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

# 编译选项
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic -Wno-unused-parameter)
endif()

set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)
set(CMAKE_INSTALL_RPATH "$ORIGIN")

# 自动查找所有源文件
file(GLOB_RECURSE SRC_FILES
  src/*.cpp
)

# 自动查找所有头文件
file(GLOB_RECURSE HEADER_FILES
  include/*.h
)

file(GLOB SIMULATE_SOURCES src/simulate/*.h src/simulate/*.cc)
set_source_files_properties(${SIMULATE_SOURCES} PROPERTIES COMPILE_FLAGS "-Wno-unused-parameter -Wno-sign-compare -Wno-missing-field-initializers -Wno-psabi")
list(FILTER SIMULATE_SOURCES EXCLUDE REGEX "src/simulate/main\\.cc")

# 添加 macOS 特定的编译选项
if(APPLE)
  set(PLATFORM_SOURCES
    src/simulate/macos_gui.mm
    src/simulate/glfw_corevideo.mm
  )

  # 添加 macOS 特定的框架
  set(PLATFORM_LIBS
    "-framework Cocoa"
    "-framework CoreVideo"
  )
else()
  set(PLATFORM_SOURCES "")
  set(PLATFORM_LIBS "")
endif()

# 创建库文件
add_library(${PROJECT_NAME}_lib
  ${SRC_FILES}
  ${HEADER_FILES}
  ${SIMULATE_SOURCES}
  ${PLATFORM_SOURCES}
)

# 设置库的包含目录
target_include_directories(${PROJECT_NAME}_lib PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
  $<BUILD_INTERFACE:${backward_SOURCE_DIR}>
  $<INSTALL_INTERFACE:include>
)

if(APPLE AND CMAKE_SYSTEM_PROCESSOR STREQUAL "arm64")
  set(BACKWARD_LIBS backward)
  set(OPENGL_LIBS "-framework OpenGL" ${PLATFORM_LIBS})
  set(MUJOCO_LIBS mujoco::mujoco)
else()
  # set(BACKWARD_LIBS backward dw elf)
  set(BACKWARD_LIBS backward bfd)
  set(OPENGL_LIBS OpenGL)
  set(MUJOCO_LIBS mujoco)
endif()

target_link_libraries(${PROJECT_NAME}_lib
  ${MUJOCO_LIBS}
  glfw
  ${OPENGL_LIBS}
  lodepng
  yaml-cpp
  ament_index_cpp::ament_index_cpp
  rclcpp::rclcpp
  ${BACKWARD_LIBS}
)

ament_target_dependencies(${PROJECT_NAME}_lib ${MSG_DEPS} sensor_msgs)

# 修改主程序，链接到我们的库
add_executable(vita_mujoco src/vita_mujoco.cpp)
target_link_libraries(vita_mujoco PUBLIC
  ${PROJECT_NAME}_lib
  ${BACKWARD_LIBS}
)

# 安装目标
install(TARGETS
  vita_mujoco
  ${PROJECT_NAME}_lib
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY config/
  DESTINATION share/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)

  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)

  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
