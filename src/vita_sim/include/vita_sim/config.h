// Copyright 2025 VitaDynamics Limited

#pragma once

#include <string>
#include <unordered_set>
#include <vector>

namespace vita_sim {
struct SimConfig {
  std::string robot = "vita00";
  std::string robot_scene = "scene.xml";

  std::string joystick_topic = "/joy";
  std::string low_cmd_topic = "/sim/lowcmd";
  std::string low_state_topic = "/sim/lowstate";
  std::string rt_low_state_topic = "/rt/lowstate";

  bool limit_pause = false;
  bool rt_state_control_mode = false;
  int history_states = 10'000;
  int history_buffer = 100'000'000;
  float figure_range = 1.0f;
  int low_state_hz = 500;

  float joy_vel_x_factor = 1.0;
  float joy_vel_y_factor = 1.0;
  float joy_ang_factor = 1.0;

  std::vector<std::string> vis_options{};

  // 减速比补偿配置
  bool gear_ratio_compensation_enable = true;
  double calf_gear_ratio = 2.0;
  std::unordered_set<int> calf_joint_indices = {2, 5, 8, 11};
  double ssm_kp = 40.0;  // 系统状态机的kp值
};

SimConfig LoadConfig(const std::string &config_file);
std::string ScenePath(const SimConfig &config);
void PrintTopic(const SimConfig &config);
}  // namespace vita_sim
