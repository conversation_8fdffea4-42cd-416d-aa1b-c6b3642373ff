// Copyright 2025 VitaDynamics Limited

#include "msg_queue.h"

namespace vita_sim {
template <typename Msg> void MessageQueue<Msg>::put(Msg &msg) {
  {
    std::lock_guard<std::mutex> lock(queueMutex_);
    if (queue_.size() == capacity_)
      queue_.pop_front();
    queue_.push_back(std::make_shared<Msg>(msg));
  }
  queueCond_.notify_one();
}

template <typename Msg>
std::unique_ptr<Msg> MessageQueue<Msg>::get(int timeoutMillis) {
  std::unique_lock<std::mutex> lock(queueMutex_);
  if (timeoutMillis <= 0) {
    queueCond_.wait(lock, [this] { return !queue_.empty(); });
  } else {
    // wait_for returns false if the return is due to timeout
    auto timeoutOccured =
        !queueCond_.wait_for(lock, std::chrono::milliseconds(timeoutMillis),
                             [this] { return !queue_.empty(); });
    if (timeoutOccured)
      return nullptr;
  }

  auto msg = queue_.front()->move();
  queue_.pop();
  return msg;
}

template <typename Msg> std::shared_ptr<Msg> MessageQueue<Msg>::tryGet() {
  std::unique_lock<std::mutex> lock(queueMutex_);
  if (!queue_.empty()) {
    auto msg = queue_.back();
    queue_.pop_front();
    return msg;
  } else {
    return {nullptr};
  }
}

template <typename Msg> size_t MessageQueue<Msg>::size() {
  std::unique_lock<std::mutex> lock(queueMutex_);
  auto size = queue_.size();
  return size;
}
} // namespace vita_sim
