// Copyright 2025 VitaDynamics Limited

#pragma once

#include <functional>
#include <memory>
#include <thread>

#include <mujoco/mjvisualize.h>
#include "agent.h"
#include "vita_sim/config.h"
namespace mujoco {
class Simulate;
class Simulator {
public:
  Simulator();
  ~Simulator();

  void Start(const vita_sim::SimConfig &sim_config);

  void SetModelCallback(std::function<void(Simulate&)> cb);

private:
  std::unique_ptr<Simulate> sim_;
  std::unique_ptr<std::thread> physics_thread_;
  std::function<void(Simulate&)> model_callback_;
  std::unique_ptr<std::thread> user_thread_;
  std::shared_ptr<vita_sim::Agent> agent_;

  mjvCamera cam_;
  mjvOption opt_;
  mjvPerturb pert_;
};
} // namespace mujoco
