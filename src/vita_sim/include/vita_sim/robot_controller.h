// Copyright 2025 VitaDynamics Limited

#pragma once

#include <atomic>
#include <functional>
#include <mujoco/mujoco.h>
#include <mutex>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/joy.hpp>

#ifndef USE_UNITREE_GO
#include <lowlevel_msg/msg/low_cmd.hpp>
#include <lowlevel_msg/msg/low_state.hpp>
#else
#include <unitree_go/msg/low_cmd.hpp>
#include <unitree_go/msg/low_state.hpp>
#endif

#include "vita_sim/config.h"

namespace mujoco {
class Simulate;
}

namespace vita_sim {

#ifndef USE_UNITREE_GO
using LowCmdMsg = lowlevel_msg::msg::LowCmd;
using LowStateMsg = lowlevel_msg::msg::LowState;
#else
using LowCmdMsg = unitree_go::msg::LowCmd;
using LowStateMsg = unitree_go::msg::LowState;
#endif

// 单腿关节结构体
struct LegJoints {
  int hip_id{-1};
  int thigh_id{-1};
  int calf_id{-1};
  double hip_pos{0.0};
  double thigh_pos{0.0};
  double calf_pos{0.0};
};

// 四足机器人关节结构体
struct QuadrupedJoints {
  LegJoints fr;  // Front Right (右前腿)
  LegJoints fl;  // Front Left  (左前腿)
  LegJoints rr;  // Rear Right  (右后腿)
  LegJoints rl;  // Rear Left   (左后腿)
};

class RobotController : public rclcpp::Node {
 public:
  RobotController(mujoco::Simulate &sim, const SimConfig &sim_config);
  ~RobotController();

  void CmdCallback(const typename LowCmdMsg::SharedPtr msg);
  void JoyCallback(const sensor_msgs::msg::Joy::SharedPtr msg);
  void RtLowCmdCallback(const typename LowStateMsg::SharedPtr msg);
  void PublishStateCallback();
  void Spin();
  void ApplyJointPositions(const mjModel *m, mjData *d);
  void CheckLimits(const mjModel *m, mjData *d);

 private:
  void UpdateControl();
  void CheckSimulationReset();
  void UpdateGhostPosition(const typename LowCmdMsg::SharedPtr &cmd);
  void InitJointIds();
  void ApplyInitialJointState(const mjModel *m, mjData *d);
  void ProcessRtStateControl(const typename LowStateMsg::SharedPtr &msg);

  // 仿真相关
  mujoco::Simulate &sim_;
  double last_sim_time_{0.0};

  // ROS2 通信相关
  typename rclcpp::Subscription<LowCmdMsg>::SharedPtr cmd_sub_;
  typename rclcpp::Subscription<sensor_msgs::msg::Joy>::SharedPtr joy_sub_;
  typename rclcpp::Subscription<LowStateMsg>::SharedPtr rt_state_sub_;
  typename rclcpp::Publisher<LowStateMsg>::SharedPtr state_pub_;
  rclcpp::TimerBase::SharedPtr timer_;
  // same as the default calculation timestep (in second) in mujoco
  float cmd_timeout_{0.002};

  // 命令和状态相关
  std::mutex cmd_mutex_;
  typename LowCmdMsg::SharedPtr last_cmd_;
  rclcpp::Time last_cmd_time_;
  std::atomic<bool> has_received_cmd_{false};
  uint32_t state_id_{0};
  bool rt_state_control_mode_{false};  // 是否从 rt_state 中提取控制命令

  // 关节控制相关
  std::mutex joints_mutex_;
  QuadrupedJoints ghost_joints_;
  bool has_custom_joint_pos_ = false;
  bool limit_pause_ = false;

  std::atomic<bool> initial_state_applied_{false};
  typename LowStateMsg::SharedPtr initial_state_msg_;
  typename LowStateMsg::SharedPtr latest_state_msg_;

  // 减速比补偿配置
  bool gear_ratio_compensation_enable_;
  double calf_gear_ratio_sq_;  // 预计算的减速比平方
  double ssm_kp_;  // 系统状态机的kp值
  bool calf_joint_flags_[12];  // 预计算的小腿关节标志数组，避免运行时查找
};

}  // namespace vita_sim
