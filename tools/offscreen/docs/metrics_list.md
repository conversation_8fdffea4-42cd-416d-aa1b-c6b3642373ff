# Check List of Metrics

## Content
1. [Common Metrics](#common-metrics)
    1. [Stability](#stability)
    2. [Kinematics](#kinematics)
    3. [Dynamics](#dynamics)
    4. [Function Safety](#function-safety)
    5. [Energy Balance](#energy-balance)
2. [Metrics w.r.t Task](#metrics-wst-task)
    1. [Stand On](#stand-on)
    2. [Trotting](#trotting)
    3. [Flipping](#flipping)

## 1. Common Metrics
### 1.1 Stability
|指标名称|函数名|描述|
|----|----|----|
|robot_state_stable|robot_state_stable|关节角度是否稳定|

### 1.2 Kinematics
|指标名称|函数名|描述|
|----|----|----|
|com_in_support_area|com_in_support_area|质心是否在支撑区域内|

### 1.3 Dynamics
|指标名称|函数名|描述|
|----|----|----|
|zmp_in_support_polygon|zmp_in_support_polygon|零力矩点是否在支撑多边形内|
|impact_force_magnitude|impact_force_magnitude|冲击力峰值，单腿最大冲击力和自重的比值|
|torque_smoothness|torque_smoothness|关节力矩平滑度d²τ/dt² 的 RMSE|


### 1.4 Function Safety
|指标名称|函数名|描述 
|----|----|----|
|limit_geometry|limit_geometry|关节角度是否在限制范围内|
|limit_max_ctrl_duration|limit_max_ctrl_duration|控制信号持续满载时间是否过长|

### 1.5 Energy Balance
|指标名称|函数名|描述|
|----|----|----|
|ctrl_analysis|ctrl_analysis|完成任务电机控制信号数据分析|
|energy_consumption|energy_consumption|styling: 能量消耗（tau x dq）|

## 2. Metrics w.r.t Task
### 2.1 Stand On
|指标名称|函数名|描述|
|----|----|----|
|stand_on_duration|stand_on_duration|完成站立任务耗时|

### 2.2 Trotting  
|指标名称|函数名|描述|
|----|----|----|
|speed_analysis|speed_analysis|行走速度分析|

### 2.3 Flipping
|指标名称|函数名|描述|
|----|----|----|
|balance_in_air|balance_in_air|styling: 空中平衡性，只绕单轴旋转|
|self_contact_in_air|self_contact_in_air|styling: 空中自接触|
|flip_height|flip_height|styling: 空翻质心高度不得超出上下限值|
|contacts_when_landing|contacts_when_landing|styling: 落地和地面只能有足端接触|

To be continued...
