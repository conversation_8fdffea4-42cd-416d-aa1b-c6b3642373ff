import unittest
from mcap_reader import read_mcap_messages
import numpy as np


class TestMcapReader(unittest.TestCase):
    def test_read_mcap_messages(self):
        cmds = read_mcap_messages("/home/<USER>/Data/rosbag2_2025_03_15-16_19_36_0.mcap")

        # 获取特定时间戳的命令
        cmd = cmds[1742026790421720432]

        # 预期的q值列表
        expected_q_values = [
            -0.12111923098564148,
            2.484999656677246,
            -2.238706588745117,
            0.29008936882019043,
            2.7001051902770996,
            -2.3195886611938477,
            0.07581277191638947,
            2.4324686527252197,
            -2.04298734664917,
            -0.20343735814094543,
            2.046635866165161,
            -2.2800087928771973,
            0.0,  # 13-20号电机的预期值都是0
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
        ]

        # 检查命令列表长度
        self.assertEqual(len(cmd), len(expected_q_values), "电机命令数量不匹配")

        # 对每个电机的q值进行检查
        for i, (motor_cmd, expected_q) in enumerate(zip(cmd, expected_q_values)):
            self.assertAlmostEqual(
                motor_cmd.q, expected_q, places=6, msg=f"电机 {i+1} 的q值不匹配"
            )


if __name__ == "__main__":
    unittest.main()
