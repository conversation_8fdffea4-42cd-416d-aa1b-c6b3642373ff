# metrics.yaml
metrics:
  common:
    Stability:
      - name: is_robot_state_stable
        function: is_robot_state_stable
        description: 关节角度是否稳定
        params:
          q_std_threshold: 0.01
          walking_freq_min: 0.5
          walking_freq_max: 3.0
          freq_energy_threshold: 0.7

    Kinematics:
      - name: cog_in_support_area
        function: cog_in_support_area
        description: 质心是否在支撑区域内
        params:
          support_area_margin: 0.005
          contact_force_threshold: 15.0

    Dynamics:
      - name: zmp_in_support_polygon
        function: zmp_in_support_polygon
        description: 零力矩点是否在支撑多边形内
        params:
          support_area_margin: 0.005
          contact_force_threshold: 15.0
      - name: impact_force_magnitude
        function: impact_force_magnitude
        description: 冲击力峰值，单腿最大冲击力和自重的比值
        params:
          max_impact_ratio: 5.35

    Function_Safety:
      - name: limit_geometry
        function: limit_geometry
        description: 关节角度是否在限制范围内
        params:
          joint_limit_percent: 0.99
      - name: limit_max_ctrl_duration
        function: limit_max_ctrl_duration
        description: 控制信号持续满载时间是否过长
        params:
          max_ctrl_duration: 5
          joint_ctrl_limit_percent: 0.99

    Energy_Balance:
      - name: ctrl_analysis
        function: ctrl_analysis
        description: 完成任务电机控制信号数据分析
      - name: energy_consumption
        function: energy_consumption
        description: 任务能量消耗
        params:
          max_energy_consumption: 100.0

  task_metrics:
    Stand_on:
      - name: stand_on_duration
        function: stand_on_duration
        description: 完成站立任务耗时
        params:
          vel_threshold: 0.05  # 速度阈值（m/s）
          max_duration_threshold: 3
          min_duration_threshold: 1
          min_consecutive_steps: 150  # 连续上升的时间步数
          window_size: 500  # 移动平均窗口大小（需为奇数）

    Trotting:
      - name: speed_analysis
        function: speed_analysis
        description: 行走速度分析
    
    Flipping:
      - name: balance_in_air
        function: balance_in_air
        description: 空中平衡性
        params:
          flip_type: side # {"front_back", "side", "in_place"}
          secondary_angle_threshold: 0.2  # 主转轴以外的轴翻滚角度阈值 (弧度)
      - name: self_contact_in_air
        function: self_contact_in_air
        description: 空中自接触
        params:
          contact_force_threshold: 1.0
      - name: flip_height
        function: flip_height
        description: 空翻高度
        params:
          max_height: 1.0
          min_height: 0.1
      - name: contacts_when_landing
        function: contacts_when_landing
        description: 落地和地面只能有足端接触
        params:
          max_contact_force: 1.0 

tasks:
  default:
    common_metrics: [Stability, Kinematics, Dynamics, Energy_Balance, Function_Safety]
    task_type: [Flipping]

