# 离线仿真器

## 安装依赖

```bash
uv sync
```

## 使用方法

注意请提前修改 [main.py](./main.py) 中的 `model_path` 为你的模型路径。

```py
model_path = "/home/<USER>/Vita/vitamin_sim/src/robot/vita00/scene.xml"
```

运行方式

```bash
uv run main.py /path/to/your/mcap/file.mcap

# example
uv run main.py \
-m /root/vitasim_cloud/models/vita00/scene.xml \
-o /root/vitasim_cloud/output \
-c /root/vitasim_cloud/offscreen/metrics.yaml \
-b /root/vitasim_cloud/mcap/rosbag2_2025_03_15-16_19_36_0.mcap

```

## 镜像构建

```bash
# 纯开环/闭环仿真镜像
cd ../../
colcon build --packages-select lowlevel_msg sim_msg
docker build . \
-t vitasim_cloud:v_0.0.0 \
-f ./vitasim_cloud.Dockerfile

# 包含模型推理基础库的仿真镜像
cd ../../
colcon build --packages-select lowlevel_msg sim_msg vita_flow
docker build . \
-t vitasim_dag:v_0.0.0 \
-f ./vitasim_cloud.Dockerfile

# 镜像中使用运行脚本
bash run_simulation.sh -c METRIC.yaml -b ROSBAG2.mcap

# 默认闭环，使用开环仿真的话参考以下
bash run_simulation.sh -c METRIC.yaml -b ROSBAG2.mcap -l open -t 60
```
