"""评测指标"""

import importlib
import logging
import json
import yaml
import numpy as np

from utils import Utils
import mujoco


class MetricManager:
    """评测指标管理器"""

    def __init__(
        self,
        yaml_file: str,
        model: mujoco.MjModel,
        task_name="default",
        max_steps=30_000,
    ):
        self.task_name = task_name
        with open(yaml_file, "r", encoding="utf-8") as file:
            self.metric_config = yaml.safe_load(file)
        task_config = self.metric_config["tasks"][task_name]
        self.metric_params = {}
        self.register_metric(task_config)

        self.initial_model = model
        self.num_joints = self.initial_model.nu
        self.timestep = self.initial_model.opt.timestep

        self.model_hist = []
        self.data_hist = []

        self.max_steps = max_steps
        self.current_step = 0

        # 关节几何限位值, 第一位是joint_fixed_world, 不在考虑范围内
        self.jnt_limits = np.array(
            [
                self.initial_model.jnt_range[1:, 0],
                self.initial_model.jnt_range[1:, 1],
            ]
        ).T
        # 关节施力限位值
        self.jnt_max_ctrl = np.array(
            [
                self.initial_model.jnt_actfrcrange[:, 0],
                self.initial_model.jnt_actfrcrange[:, 1],
            ]
        ).T

        # 关节角度
        self.np_sim_q = np.zeros((max_steps, self.num_joints))
        # 关节速度
        self.np_sim_dq = np.zeros((max_steps, self.num_joints))
        # 受力为xyz轴分量
        self.np_sim_forcetorque = np.zeros((max_steps, 4, 6))
        # 质心位置 (x, y, z)
        self.np_sim_com = np.zeros((max_steps, 3))
        # 四足位置
        self.np_sim_foot_pos = np.zeros((max_steps, 4, 3))
        # 输入到模型的Ctrl信号, M默认被MuJoCo按照actuatorfrcrange给clip过
        self.np_sim_ctrl = np.zeros((max_steps, self.num_joints))
        # 质心角速度
        self.np_sim_angular_vel = np.zeros((max_steps, 3))
        self.prev_rot = None
        # 滞空状态
        self.is_in_air = np.zeros(self.max_steps, dtype=bool)
        # 关节力矩
        self.np_sim_tau = np.zeros((max_steps, self.num_joints))

        # 完整接触信息
        self.contacts = [[] for _ in range(max_steps)]

        self.gravity = self.initial_model.opt.gravity[2]
        self.robot_weight = np.sum(self.initial_model.body_mass) * abs(self.gravity)

        self.support_polygon_vertices = None

    def push_step_data(self, mj_model: mujoco.MjModel, mj_data: mujoco.MjData):
        """获取并计算每一步需要的数据"""
        index = self.current_step % self.max_steps
        num_j = self.num_joints
        self.np_sim_q[index] = mj_data.sensordata[:num_j]
        self.np_sim_dq[index] = mj_data.sensordata[num_j : num_j * 2]

        self.np_sim_forcetorque[index] = Utils.get_floor_foot_forcetorque(
            mj_model, mj_data
        )

        self.np_sim_com[index] = mj_data.subtree_com[
            Utils.get_id_from_body_name(mj_model, Utils.base_body_name)
        ]

        self.np_sim_foot_pos[index] = Utils.get_foot_pos(mj_model, mj_data)

        self.np_sim_ctrl[index] = mj_data.ctrl
        # 计算角速度并更新旋转对象
        self.np_sim_angular_vel[index], self.prev_rot = Utils.get_angular_velocity(
            mj_model, mj_data, self.prev_rot, self.timestep
        )
        # 判断机器人是否滞空
        self.is_in_air[index] = Utils.is_robot_in_air(mj_model, mj_data)

        self.np_sim_tau[index] = mj_data.ctrl

        self.contacts[index] = []
        for con_id, contact in enumerate(mj_data.contact):
            # 保存接触点的详细信息
            self.contacts[index].append(
                Utils.get_contact_info(mj_model, mj_data, con_id, contact)
            )
        self.current_step += 1

    def register_metric(self, task_config):
        """根据任务配置注册指标类实例"""
        selected_metrics = []
        for metric in task_config.get("metrics", []):
            selected_metrics.append(metric.copy())
        self.metrics = {}
        self.metric_info = {}
        for metric_config in selected_metrics:
            name = metric_config["name"]
            class_path = metric_config["class"]
            try:
                # 加载指标类
                module_path, class_name = class_path.rsplit('.', 1)
                module = importlib.import_module(module_path)
                metric_class = getattr(module, class_name)
                # 实例化并存储
                instance = metric_class(self)
                self.metrics[name] = instance
                # 存储元数据
                self.metric_info[name] = {
                    "description": metric_config.get("description", ""),
                    "params": metric_config.get("params", {})
                }
            except (ImportError, AttributeError) as e:
                logging.error(f"加载指标 {name} 失败: {e}")

    def run_metrics(self, json_path):
        """执行所有指标并生成结果"""

        results = {"summary": {}, "all_metrics": []}  # 所有指标统一存储

        for name, metric in self.metrics.items():
            config = self.metric_info[name]
            try:
                result = metric.execute()
                passed = result["passed"]
                details = result.get("details", [])
            except Exception as e:
                logging.error(f"指标 {name} 执行失败: {str(e)}")
                passed = False
                details = [{"error": f"执行时发生异常: {str(e)}"}]

            entry = {
                "name": name,
                "result": passed,
                "description": config["description"],
                "params": config["params"],
                "details": details
            }
            results["all_metrics"].append(entry)

        # 统计总结果
        passed_count = sum(1 for entry in results["all_metrics"] if entry["result"])
        failed_count = len(results["all_metrics"]) - passed_count
        failed_metrics = [
            entry["name"] for entry in results["all_metrics"] if not entry["result"]
        ]

        results["summary"] = {
            "status": "success" if failed_count == 0 else "fail",
            "total_metrics": len(results["all_metrics"]),
            "passed_count": passed_count,
            "failed_count": failed_count,
            "failed_metrics": failed_metrics,
        }

        self.save_metrics_to_json(json_path, results)

    def save_metrics_to_json(self, filename, results):
        """保存执行结果到 JSON 文件"""

        def default_handler(obj):
            if isinstance(obj, np.bool_ | bool):
                return bool(obj)
            raise TypeError(f"Unsupported type: {type(obj)}")

        with open(filename, "w", encoding="utf-8") as json_file:
            json.dump(results, json_file, default=default_handler, ensure_ascii=False)
    def check_support_polygon_vertices(self, contact_force_threshold):
        """判断支撑平面是否已经计算过, 保证只计算一次"""
        if self.support_polygon_vertices is not None:
            return True
        current_index = (self.current_step - 1) % self.max_steps
        forces = self.np_sim_forcetorque[current_index][:, :3]
        foot_positions = self.np_sim_foot_pos[current_index]
        self.support_polygon_vertices = Utils.get_support_polygon(
            forces, foot_positions, contact_force_threshold
        )
        if self.support_polygon_vertices is None:
            return False
        return True
