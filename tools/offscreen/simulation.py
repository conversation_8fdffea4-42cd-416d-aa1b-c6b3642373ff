import mujoco
import logging
import signal
import threading
import time
from rclpy.executors import SingleThreadedExecutor
from rclpy.executors import ExternalShutdownException
import rclpy
from close_loop_node import CloseLoopSubNode, CloseLoopPubNode
from metric_manager import <PERSON>ricManager
from video_recorder import VideoRecorder
from mcap_reader import read_mcap_messages


class OffscreenSimulator:
    def __init__(
        self, model_path, timeout=60.0, is_close_loop=True, width=640, height=480
    ):
        self._shutdown_event = threading.Event()
        self._is_shutdown = False
        signal.signal(signal.SIGINT, self._handle_sigint)
        self.model = mujoco.MjModel.from_xml_path(model_path)
        self.data = mujoco.MjData(self.model)
        self.width = width
        self.height = height
        self.renderer = mujoco.Renderer(
            self.model, width=self.width, height=self.height
        )

        self.is_close_loop = is_close_loop
        self.sim_timeout = timeout
        self.sim_time = 0.0  # 仿真时间
        self.last_render_time = 0.0  # 上次渲染的仿真时间
        self.render_interval = 1.0 / 60  # 渲染间隔（60fps）

        self.camera = mujoco.MjvCamera()
        mujoco.mjv_defaultFreeCamera(self.model, self.camera)
        self.camera.type = mujoco.mjtCamera.mjCAMERA_TRACKING  # 只修改为跟随模式
        self.camera.trackbodyid = 0  # 跟随 base 身体（ID为0）

        if self.is_close_loop:
            self.setup_closed_loop()

    def step(self, cmd=None, step_size=0.002):
        """执行一步仿真，可选是否应用控制命令"""
        if cmd is not None and len(cmd) >= self.model.nu:
            self._apply_control(cmd)

        mujoco.mj_step(self.model, self.data)
        self.sim_time += step_size

        should_render = (self.sim_time - self.last_render_time) >= self.render_interval
        if should_render:
            self.last_render_time = self.sim_time
            return self.capture_frame()
        return None

    def _apply_control(self, cmd):
        """应用控制命令到仿真器"""
        for i in range(self.model.nu):
            self.data.ctrl[i] = (
                cmd[i].tau
                + cmd[i].kp * (cmd[i].q - self.data.sensordata[i])
                + cmd[i].kd * (cmd[i].dq - self.data.sensordata[i + self.model.nu])
            )

    def capture_frame(self):
        """捕获当前帧"""
        self.renderer.update_scene(self.data, self.camera)
        return self.renderer.render()

    def after_task_check(
        self, metric_manager: MetricManager, duration: float
    ):
        frames = []
        duration = 3.0
        # 等于mujoco最小计算步长 默认是2ms 仿真间隔
        sim_step = self.model.opt.timestep
        steps = int(duration / sim_step)

        for _ in range(steps):
            frame = self.step()
            if frame is not None:
                frames.append(frame)
                metric_manager.push_step_data(self.model, self.data)
        return frames

    def sim_in_loop(
        self, mcap_path: str, recorder: VideoRecorder, metric_manager: MetricManager
    ):
        """
        执行仿真，并循环渲染
        """
        frames = []
        if self.is_close_loop:
            assert self.close_loop_sub_node is not None
            frames = self.sim_in_close_loop(recorder, metric_manager)
        else:
            frames = self.sim_in_open_loop(mcap_path, recorder, metric_manager)
        return frames

    def sim_in_open_loop(
        self, mcap_path: str, recorder: VideoRecorder, metric_manager: MetricManager
    ):
        """
        读取mcap文件, 并按照mcap文件中的顺序执行开环仿真
        """
        frames = []
        print("初始化...")
        free_fall_duration = 3.0
        # 等于mujoco最小计算步长 默认是2ms 仿真间隔
        sim_step = self.model.opt.timestep
        steps = int(free_fall_duration / sim_step)

        for _ in range(steps):
            frame = self.step()
            if frame is not None:
                frames.append(frame)

        print("开始读取 -> 解析 -> 执行控制命令...")
        messages = list(read_mcap_messages(mcap_path).items())

        total_duration = (messages[-1][0] - messages[0][0]) / 1e9  # 转换为秒
        print(f"总控制时长: {total_duration:.2f}秒")

        current_msg_idx = 0
        while current_msg_idx < len(messages):
            ts, cmd = messages[current_msg_idx]
            sim_time = self.sim_time - free_fall_duration
            msg_time = (ts - messages[0][0]) / 1e9

            if sim_time >= msg_time:
                if current_msg_idx < len(messages) - 1:
                    print(
                        f"执行命令 {current_msg_idx + 1}/{len(messages)}, 仿真时间: {sim_time:.3f}s"
                    )
                current_msg_idx += 1

            frame = self.step(cmd, sim_step)

            # 存储step以后的data数据
            metric_manager.push_step_data(self.model, self.data)

            if frame is not None:
                frames.append(frame)

            if current_msg_idx == len(messages) and sim_time < msg_time:
                current_msg_idx -= 1
        return frames

    def setup_closed_loop(self):
        rclpy.init()
        self.close_loop_sub_node = CloseLoopSubNode(self.model.nu)
        self.close_loop_pub_node = CloseLoopPubNode(self.model.nu)
        self.executor = SingleThreadedExecutor()
        self.executor.add_node(self.close_loop_sub_node)

        # Define a wrapper function for thread to handle exceptions
        def _executor_spin():
            try:
                self.executor.spin()
            except ExternalShutdownException:
                logging.info("Executor stopped due to external shutdown")
            except Exception as e:
                logging.error(f"Executor error: {str(e)}")

        self.executor_thread = threading.Thread(target=_executor_spin, daemon=True)
        self.executor_thread.start()

    def sim_in_close_loop(self, recorder: VideoRecorder, metric_manager: MetricManager):
        """Main simulation loop for closed-loop operation"""
        current_sim_time = 0
        last_sim_time = time.time()
        frames = []
        print("start close loop simulation")
        try:
            while rclpy.ok() and not self._shutdown_event.is_set():
                if self.close_loop_sub_node is None:
                    raise RuntimeError("Close-loop node not initialized")
                # Check command timeout
                current_cmd_time = self.close_loop_sub_node.get_last_time()
                cmd_arriving_time = (
                    self.close_loop_sub_node.get_clock().now() - current_cmd_time
                ).nanoseconds / 1e9

                if cmd_arriving_time > self.sim_timeout:
                    print(f"{self.sim_timeout}秒未收到控制信号, 退出仿真")
                    raise TimeoutError(
                        f"No control signal received for {self.sim_timeout} seconds"
                    )

                # Get control signal
                lowcmd = self.close_loop_sub_node.get_current_ctrl()
                frame = self.step(lowcmd)
                current_sim_time = time.time()
                elapsed = current_sim_time - last_sim_time
                # 存储step以后的data数据
                metric_manager.push_step_data(self.model, self.data)
                self.close_loop_pub_node.enqueue_sensordata(self.data.sensordata)

                if frame is not None:
                    frames.append(frame)
                # Control simulation frequency
                sleep_time = self.model.opt.timestep - elapsed
                time.sleep(max(0, sleep_time))
                last_sim_time = time.time()

        except TimeoutError as e:
            logging.info(str(e))
        finally:
            self.ros_node_shutdown()
        return frames

    def ros_node_shutdown(self):
        if self._is_shutdown:
            return

        # Gracefully stop executor
        if hasattr(self, "executor"):
            self.executor.shutdown()
            time.sleep(0.1)  # Allow executor to process shutdown

        # Wait for thread with timeout
        if hasattr(self, "executor_thread"):
            try:
                self.executor_thread.join(timeout=5)  # Wait up to 5 seconds
            except RuntimeError:
                pass

        # Destroy nodes after executor stops
        if hasattr(self, "close_loop_sub_node"):
            self.close_loop_sub_node.destroy_node()
        if hasattr(self, "close_loop_pub_node"):
            self.close_loop_pub_node.destroy_node()

        # Finalize RCLPY context
        if rclpy.ok():
            rclpy.shutdown()

        self._is_shutdown = True

    def _handle_sigint(self, signum, frame):
        logging.info("SIGINT received, preparing to exit")
        self._shutdown_event.set()
