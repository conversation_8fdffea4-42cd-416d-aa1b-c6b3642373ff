import os
import argparse
import logging
from pathlib import Path

from simulation import OffscreenSimulator
from metric_manager import MetricManager
from video_recorder import VideoRecorder

def parse_args():
    """
    解析命令行参数并返回参数对象。

    Args:
        该函数通过argparse模块解析以下命令行参数:
        -b, --mcap_path (str): MCAP文件路径 (可选参数)
        -c, --metric_config (str): metrics.yaml文件路径 (必需参数)
        -m, --model_path (str): scene.xml文件路径(必需参数)
        -o, --output_dir (str): 输出文件夹路径 (必需参数)

    Returns:
        argparse.Namespace: 包含解析后的参数对象，属性对应各个命令行选项
    """
    parser = argparse.ArgumentParser(description="离线仿真器")
    parser.add_argument(
        "-b",
        "--mcap_path",
        type=str,
        help="MCAP文件路径",
    )
    parser.add_argument(
        "-c",
        "--metric_config",
        type=str,
        required=True,
        help="metrics.yaml文件路径",
    )
    parser.add_argument(
        "-m",
        "--model_path",
        type=str,
        required=True,
        help="scene.xml文件路径",
    )
    parser.add_argument(
        "-o",
        "--output_dir",
        type=str,
        required=True,
        help="输出文件夹路径",
    )
    parser.add_argument(
        "-l",
        "--loop_type",
        type=str,
        required=True,
        help="仿真类型, 可选值: open, close",
    )

    parser.add_argument(
        "-t",
        "--timeout",
        type=float,
        required=True,
        help="超时时间, 单位秒",
    )
    parser.add_argument(
        "--debug",
        default=False,
        help="Enable debug logging to console"
    )

    return parser.parse_args()


def scp(mp4_path):
    import subprocess

    try:
        print("正在传输视频到 Mac...")
        subprocess.run(["scp", mp4_path, "mac:~/Data/"], check=True)
        print("视频传输完成")
    except subprocess.CalledProcessError as e:
        print(f"视频传输失败: {e}")


def setup_logging(output_dir, is_debug):
    """
    设置日志记录器，将日志记录到文件。

    日志记录器将日志记录到文件，文件名为"simulation.log", 日志级别为INFO。
    """
    app_log = os.path.join(output_dir, "simulation.log")
    logging.basicConfig(
        filename=app_log,
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    if not is_debug:
        return
    logging.getLogger().setLevel(logging.DEBUG)
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        "%(asctime)s [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(console_formatter)
    logging.getLogger().addHandler(console_handler)


def main():
    args = parse_args()
    model_path = args.model_path
    if not args.mcap_path:
        mcap_path = "vita_close_loop_sim"
    else:
        mcap_path = args.mcap_path
    output_dir = args.output_dir
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    setup_logging(output_dir, args.debug)

    mp4_path = os.path.join(output_dir, Path(mcap_path).stem + ".mp4")
    is_close_loop = args.loop_type == "close"
    sim_timeout = args.timeout

    simulator = OffscreenSimulator(
        model_path, timeout=sim_timeout, is_close_loop=is_close_loop
    )
    recorder = VideoRecorder(mp4_path)
    metric_manager = MetricManager(args.metric_config, simulator.model)

    frames = simulator.sim_in_loop(mcap_path, recorder, metric_manager)

    after_check_frames = simulator.after_task_check(metric_manager, duration=3.0)
    frames += after_check_frames
    for frame in frames:
        recorder.write_frame(frame)
    recorder.release()

    metric_manager.run_metrics(
        os.path.join(output_dir, f"{Path(mcap_path).stem}_metric.json")
    )
    simulator.renderer.close()
    print(f"仿真完成, 视频已保存到: {mp4_path}")
    # scp(mp4_path)


if __name__ == "__main__":
    main()
