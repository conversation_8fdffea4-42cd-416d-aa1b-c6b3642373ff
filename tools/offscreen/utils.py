"""工具类"""

import logging
import time
from functools import wraps
from scipy.spatial import ConvexHull, QhullError
from scipy.spatial.transform import Rotation as RRR
import numpy as np

import mujoco

def timeit(func):
    """装饰器：自动记录函数执行时间并记录日志"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start
        logging.info("[%s] 执行耗时: %.4f秒", func.__qualname__, elapsed)
        return result
    return wrapper
def metric_params(func):
    def wrapper(self, *args, **kwargs):
        # 存储参数到实例属性
        self.params = kwargs.copy()
        return func(self, *args, **kwargs)
    return wrapper


class Utils:
    """静态工具类"""

    foot_names = ["FR", "FL", "RR", "RL"]
    floor_name = "floor"
    base_body_name = "base"

    @classmethod
    def get_name_from_geom_id(cls, mj_model, geomid):
        """wrapper for mujoco.mj_id2name of GEOM type"""
        return mujoco.mj_id2name(mj_model, mujoco.mjtObj.mjOBJ_GEOM, geomid)

    @classmethod
    def get_id_from_geom_name(cls, mj_model, geom_name):
        """wrapper for mujoco.mj_name2id  of GEOM type"""
        return mujoco.mj_name2id(mj_model, mujoco.mjtObj.mjOBJ_GEOM, geom_name)

    @classmethod
    def get_name_from_body_id(cls, mj_model, bodyid):
        """wrapper for mujoco.mj_id2name of BODY type"""
        return mujoco.mj_id2name(mj_model, mujoco.mjtObj.mjOBJ_BODY, bodyid)

    @classmethod
    def get_id_from_body_name(cls, mj_model, body_name):
        """wrapper for mujoco.mj_name2id of BODY type"""
        return mujoco.mj_name2id(mj_model, mujoco.mjtObj.mjOBJ_BODY, body_name)

    @classmethod
    def get_foot_ids(cls, mj_model):
        """获取足端ID列表"""
        foot_ids = []
        for foot_name in cls.foot_names:
            foot_id = cls.get_id_from_geom_name(mj_model, foot_name)
            if foot_id != -1:
                foot_ids.append(foot_id)
        return foot_ids

    @classmethod
    def get_floor_foot_forcetorque(cls, mj_model, mj_data):
        """获取地板和足端接触面的力和力矩"""
        foot_indices = {name: i for i, name in enumerate(cls.foot_names)}
        force_torque = np.zeros((4, 6))

        for index, contact in enumerate(mj_data.contact):
            geom1 = cls.get_name_from_geom_id(mj_model, contact.geom1)
            geom2 = cls.get_name_from_geom_id(mj_model, contact.geom2)
            if not geom1 or not geom2:
                continue
            if cls.floor_name != geom1 and cls.floor_name != geom2:
                continue

            foot_found = None
            for foot in cls.foot_names:
                if foot == geom1 or foot == geom2:
                    foot_found = foot
                    break

            if not foot_found:
                continue
            foot_idx = foot_indices[foot_found]
            # 获取接触力（6D：zxy力+zxy力矩）
            mujoco.mj_contactForce(mj_model, mj_data, index, force_torque[foot_idx])
        return force_torque

    @classmethod
    def get_foot_pos(cls, mj_model, mj_data):
        """获取足端几何位置"""
        foot_pos = np.zeros((4, 3))
        for index, foot_name in enumerate(cls.foot_names):
            foot_id = cls.get_id_from_geom_name(mj_model, foot_name)
            if foot_id == -1:
                continue
            foot_pos[index] = mj_data.geom_xpos[foot_id]
        return foot_pos

    @classmethod
    def distance_to_polygon(cls, point, polygon_vertices):
        """计算点到多边形边界的带符号距离"""
        inside = cls.is_point_inside_polygon(point, polygon_vertices)
        min_dist = cls.min_distance_to_polygon_edges(point, polygon_vertices)
        # 返回带符号的距离：内部为负值，外部为正值
        return -min_dist if inside else min_dist

    @classmethod
    def is_point_inside_polygon(cls, point, polygon_vertices):
        """右射线法判断点是否在多边形内部"""
        p_x, p_y = point
        n_poly = len(polygon_vertices)
        inside = False
        p1x, p1y = polygon_vertices[0]
        xints = 0
        for i in range(n_poly + 1):
            p2x, p2y = polygon_vertices[i % n_poly]
            if p_y > min(p1y, p2y):
                if p_y <= max(p1y, p2y):
                    if p_x <= max(p1x, p2x):
                        if p1y != p2y:
                            xints = (p_y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or p_x <= xints:
                            inside = not inside
            p1x, p1y = p2x, p2y
        return inside

    @classmethod
    def min_distance_to_polygon_edges(cls, point, polygon_vertices):
        """计算点到多边形所有边的最小距离"""
        min_dist = float("inf")
        n_poly = len(polygon_vertices)
        for i in range(n_poly):
            v_1 = polygon_vertices[i]
            v_2 = polygon_vertices[(i + 1) % n_poly]
            dist = cls.distance_point_segment(point, v_1, v_2)
            if dist < min_dist:
                min_dist = dist
        return min_dist

    @classmethod
    def distance_point_segment(cls, point, seg_start, seg_end):
        """计算点到线段的最短距离"""
        x_0, y_0 = point
        x_1, y_1 = seg_start
        x_2, y_2 = seg_end

        d_x = x_2 - x_1
        d_y = y_2 - y_1
        dr_squared = d_x * d_x + d_y * d_y
        if dr_squared == 0:
            return ((x_0 - x_1) ** 2 + (y_0 - y_1) ** 2) ** 0.5

        dist = ((x_0 - x_1) * d_x + (y_0 - y_1) * d_y) / dr_squared
        dist = max(0.0, min(1.0, dist))

        proj_x = x_1 + dist * d_x
        proj_y = y_1 + dist * d_y

        return ((x_0 - proj_x) ** 2 + (y_0 - proj_y) ** 2) ** 0.5

    @classmethod
    def cal_zmp_point(cls, forces, foot_positions):
        """计算zmp位置"""
        f_total_z = 0.0
        sum_x = 0.0
        sum_y = 0.0

        for i in range(4):
            f_z = forces[i][0]

            p_x, p_y, _ = foot_positions[i]

            sum_x += p_x * f_z
            sum_y += p_y * f_z

            f_total_z += f_z

        # 处理分母为零的情况（无接触力）
        if f_total_z < 1e-6:
            return None

        zmp_x = sum_x / f_total_z
        zmp_y = sum_y / f_total_z

        return (zmp_x, zmp_y)

    @classmethod
    def get_support_polygon(
        cls, forces: np.ndarray, foot_positions: np.ndarray, contact_threshold: float
    ) -> np.ndarray | None:
        """获取支撑多边形顶点坐标"""
        # 筛选接触地面的足部
        contact_indices = [i for i in range(4) if forces[i, 0] > contact_threshold]
        if len(contact_indices) < 2:
            return None

        # 提取接触点的二维坐标（x,y）
        contact_points = foot_positions[contact_indices, :2]

        try:
            hull = ConvexHull(contact_points)
            polygon_vertices = contact_points[hull.vertices]
            return polygon_vertices
        except (QhullError, ValueError):
            return None

    @classmethod
    def get_angular_velocity(cls, mj_model, mj_data, prev_rot, timestep):
        """通过旋转矩阵计算角速度"""
        base_body_id = cls.get_id_from_body_name(mj_model, cls.base_body_name)
        # 从 geom_xmat 获取旋转矩阵
        rotation_matrix = mj_data.geom_xmat[base_body_id].reshape(3, 3)
        current_rot = RRR.from_matrix(rotation_matrix)

        if prev_rot is not None:
            # 计算相对旋转（当前旋转 * 前一旋转的逆）
            delta_rot = current_rot * prev_rot.inv()
            angle_axis = delta_rot.as_rotvec()  # 返回 (angle * axis)
            angular_velocity = angle_axis / timestep
        else:
            angular_velocity = np.zeros(3)

        return angular_velocity, current_rot

    @classmethod
    def is_robot_in_air(cls, mj_model, mj_data):
        """判断机器人是否处于空中"""
        ground_id = cls.get_id_from_geom_name(mj_model, cls.floor_name)
        for contact in mj_data.contact:
            if contact.geom1 == ground_id or contact.geom2 == ground_id:
                return False
        return True

    @classmethod
    def get_contact_info(cls, mj_model, mj_data, con_id, contact):
        """从mujoco contact对象中获取接触点的信息"""
        geom1 = contact.geom1
        geom2 = contact.geom2
        # 获取接触点力向量
        forcetorque = np.zeros(6)
        mujoco.mj_contactForce(mj_model, mj_data, con_id, forcetorque)
        force = forcetorque[:3]  # xyz分量

        # 保存接触点的详细信息
        con_info = {
            "geom_pair_id": (geom1, geom2),
            "geom_pair_name": (
                Utils.get_name_from_geom_id(mj_model, geom1),
                Utils.get_name_from_geom_id(mj_model, geom2),
            ),
            "force": force.tolist(),
            "magnitude": np.linalg.norm(force),
        }
        return con_info
