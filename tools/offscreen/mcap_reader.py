from mcap.reader import make_reader
from rclpy.serialization import deserialize_message, serialize_message
from rosidl_runtime_py.utilities import get_message


def find_topic_by_schema_name(summary, target_schema_name):
    """
    在 mcap summary 中查找指定 schema_name 的第一个 topic

    Args:
        summary: mcap reader 的 summary
        target_schema_name: 目标 schema 名称

    Returns:
        tuple: (target_topic, target_schema) 如果找到，否则都为 None
    """
    for channel in summary.channels.values():
        if summary.schemas[channel.schema_id].name == target_schema_name:
            return channel.topic

    raise ValueError(f"No topic found with schema_name '{target_schema_name}'")


def read_mcap_messages(mcap_path):
    cmds = dict()
    with open(mcap_path, "rb") as f:
        reader = make_reader(f)

        target_topic = find_topic_by_schema_name(
            reader.get_summary(), "lowlevel_msg/msg/LowCmd"
        )

        for schema, _, message in reader.iter_messages(topics=[target_topic]):
            log_time = message.log_time
            data = message.data

            msg_type = get_message(schema.name)
            msg = deserialize_message(data, msg_type)
            cmds[log_time] = msg.motor_cmd

    return cmds
