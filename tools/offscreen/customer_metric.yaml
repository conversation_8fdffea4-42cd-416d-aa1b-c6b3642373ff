tasks:
  default:
    metrics:
      - name: "cog_in_support_area"
        class: "metric_impl_demo.CogInSupportAreaMetric"
        params:
          support_area_margin: 0.005
          contact_force_threshold: 15.0
        description: "判断重心是否在支撑区域内"

      - name: "zmp_in_support_polygon"
        class: "metric_impl_demo.ZmpInSupportPolygonMetric"
        params:
          support_area_margin: 0.005
          contact_force_threshold: 15.0
        description: "判断零点力矩是否在支撑面内"

      - name: "balance_in_air"
        class: "metric_impl_demo.BalanceInAirMetric"
        params:
          flip_type: "side"
          secondary_angle_threshold: 0.1
        description: "评测空中平衡性"

      - name: "self_contact_in_air"
        class: "metric_impl_demo.SelfContactInAirMetric"
        params:
          contact_force_threshold: 1.0
        description: "判断空翻过程中是否有自接触"

      - name: "flip_height"
        class: "metric_impl_demo.FlipHeightMetric"
        params:
          max_height: 1.5
          min_height: 0.1
        description: "判断空翻高度是否在区间内"

      - name: "contacts_when_landing"
        class: "metric_impl_demo.ContactsWhenLandingMetric"
        params:
          max_contact_force: 1.0
        description: "判断空翻高度是否在区间内"

      - name: "energy_consumption"
        class: "metric_impl_demo.EnergyConsumptionMetric"
        params:
          max_energy_consumption: 100.0
        description: "判断总能量消耗"