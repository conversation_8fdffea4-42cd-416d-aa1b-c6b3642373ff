import unittest
import numpy as np
from video_recorder import VideoRecorder


class TestVideoRecorder(unittest.TestCase):
    def test_video_writer(self):
        # 生成一个假的帧数据
        width, height = 640, 480
        fake_frame = (np.random.rand(height, width, 3) * 255).astype("uint8")
        video_path = "test_output.mp4"

        recorder = VideoRecorder(video_path, fps=30, width=width, height=height)
        try:
            for _ in range(10):
                recorder.write_frame(fake_frame)
        except Exception as e:
            self.fail(f"写入帧时出错: {e}")
        finally:
            recorder.release()


if __name__ == "__main__":
    unittest.main()
