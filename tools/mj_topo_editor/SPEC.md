# 需求

## 1. 功能概述

MuJoCo 地形编辑工具（mj_topo_editor）是一个用于生成和编辑 MuJoCo 仿真环境地形的工具。该工具支持多种地形生成方式，可以导出为 MuJoCo XML 格式。

## 2. 基础功能

### 2.1 基本几何体

- 支持添加以下类型的几何体：
  - Box（长方体）
  - Sphere（球体）
  - Capsule（胶囊体）
  - Ellipsoid（椭球体）
  - Cylinder（圆柱体）
  - Plane（平面）
- 每种几何体支持设置：
  - 位置（3D 坐标）
  - 姿态（欧拉角）
  - 尺寸（根据几何体类型不同而不同）

### 2.2 复合地形

- 楼梯地形生成
  - 可配置参数：初始位置、偏航角、台阶宽度、高度、长度、台阶数量
- 悬空楼梯生成
  - 在普通楼梯基础上增加间隙参数
- 不平整地面生成
  - 支持随机生成不规则地形
  - 可配置参数：初始位置、姿态、网格数量、基础尺寸、间距等
  - 支持随机扰动参数设置

### 2.3 高度场地形

- Perlin 噪声高度场
  - 可配置参数：位置、姿态、尺寸、高度范围
  - 支持设置 Perlin 噪声参数（octaves、persistence、lacunarity）
  - 支持平滑度调节
- 图片转高度场
  - 支持从图片生成高度场
  - 可配置参数：位置、姿态、尺寸、高度范围
  - 支持图片缩放和灰度反转

## 3. 文件操作

### 3.1 输入

- 支持读取基础场景文件（scene.xml）
- 支持导入高度场图片文件

### 3.2 输出

- 生成 MuJoCo XML 格式的场景文件
- 导出高度场图片文件

## 4. 工具函数

- 欧拉角与四元数转换
- 2D/3D 旋转变换
- 向量列表与字符串转换

## 5. 使用场景

### 5.1 机器人测试场景生成

- 生成标准测试地形（如楼梯、斜坡等）
- 生成随机不平整地面用于鲁棒性测试
- 生成特定形状的地形用于特定任务测试

### 5.2 仿真环境构建

- 快速构建包含多种地形的仿真环境
- 支持从真实地形数据生成仿真场景

## 6. 依赖

- Python >= 3.13
- NumPy：数值计算
- OpenCV：图像处理
- noise：Perlin 噪声生成

## 7. 注意事项

- 所有尺寸参数需符合 MuJoCo 的单位系统
- 高度场分辨率需权衡精度和性能
- 随机地形生成时注意参数范围的合理性
