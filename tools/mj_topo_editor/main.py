#!/usr/bin/env python3

"""MuJoCo 地形编辑工具示例"""

import argparse
import os
from terrain_generator import TerrainGenerator
import numpy as np


def create_cylinder_path(
    tg: TerrainGenerator,
    num_steps: int = 12,
    base_x: float = -4.0,
    base_y: float = -4.0,
    min_height: float = 0.08,
    max_height: float = 0.25,
    radius: float = 0.12,
    dx: float = 0.35,
    dy: float = 0.4,
    wave_amp: float = 0.3,
    wave_freq: float = 0.5,
    height_noise: float = 0.02,
):
    """创建圆柱石阶小径

    Args:
        tg: 地形生成器实例
        num_steps: 总步数
        base_x: 起始x位置
        base_y: 起始y位置
        min_height: 最低圆柱高度
        max_height: 最高圆柱高度
        radius: 圆柱半径
        dx: x方向步距
        dy: 两排之间的距离
        wave_amp: 蜿蜒幅度
        wave_freq: 蜿蜒频率
        height_noise: 高度随机扰动范围
    """
    for i in range(num_steps):
        # 计算当前步的位置和高度
        x = base_x + i * dx
        # 使用正弦函数使路径蜿蜒
        y_offset = wave_amp * np.sin(i * wave_freq)

        # 高度渐变（使用平方函数让高度变化更平缓）
        progress = (i / (num_steps - 1)) ** 2
        height = min_height + (max_height - min_height) * progress

        # 添加左右两个圆柱
        for side in [-1, 1]:
            y = base_y + y_offset + side * (dy / 2)
            # 添加一些随机扰动使高度更自然
            h_noise = np.random.uniform(-height_noise, height_noise)
            tg.AddGeometry(
                position=[x, y, height / 2 + h_noise],
                euler=[0, 0, 0.0],
                size=[radius, height / 2],  # MuJoCo中height参数是实际高度的一半
                geo_type="cylinder",
            )


def create_test_scene(robot_type: str):
    """创建测试场景

    Args:
        robot_type: 机器人类型，如 "vita00", "vita00w"
    """
    print(f"正在为 {robot_type} 创建测试场景...")

    # 初始化地形生成器
    tg = TerrainGenerator(robot_type)

    # 1. 添加基本几何体
    # 1.1 添加一个斜坡
    tg.AddBox(position=[2.0, -4.0, 0.5], euler=[0.0, -0.5, 0.0], size=[3, 1.5, 0.1])

    # 1.2 添加圆柱石阶小径
    create_cylinder_path(tg)

    # 2. 添加复合地形
    # 2.1 添加一组普通楼梯
    tg.AddStairs(
        init_pos=[1.0, 4.0, 0.0],
        yaw=0.0,
        width=0.2,
        height=0.15,
        length=1.5,
        stair_nums=8,
    )

    # 2.2 添加一组悬空楼梯
    tg.AddSuspendStairs(init_pos=[1.0, 6.0, 0.0], yaw=0.0, gap=0.05)

    # 2.3 添加不平整地面
    tg.AddRoughGround(
        init_pos=[-2.5, 5.0, 0.0],
        euler=[0, 0, 0.0],
        nums=[8, 8],
        box_size=[0.3, 0.3, 0.3],
        separation=[0.15, 0.15],
    )

    # 3. 添加高度场地形
    # 3.1 使用 Perlin 噪声生成随机地形
    tg.AddPerlinHeighField(
        position=[-1.5, 4.0, 0.0], size=[2.0, 1.5], height_scale=0.15, smooth=80.0
    )

    # 3.2 使用 VITA logo 生成地形
    vita_logo_path = os.path.join(os.path.dirname(__file__), "res", "VITA.jpg")
    tg.AddHeighFieldFromImage(
        position=[-2.5, 0.0, 0.0],  # 放在原点附近
        euler=[0, 0, -1.57],  # 不旋转
        size=[2.0, 2.0],  # 1米 x 1米的区域
        height_scale=0.05,  # logo 凸起高度为 5cm
        negative_height=0.01,  # 设置一个小的负向高度
        input_img=vita_logo_path,
        output_hfield_image="vita_terrain.png",
        image_scale=[1.0, 1.0],  # 保持原始分辨率
        invert_gray=True,  # 反转灰度值，使 logo 凸起
    )

    # 保存场景
    tg.Save()
    print(f"场景已保存到: {tg.output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MuJoCo 地形编辑工具")
    parser.add_argument(
        "--robot", type=str, default="vita00", help="机器人类型 (vita00 或 vita00w)"
    )
    args = parser.parse_args()

    if args.robot not in ["vita00", "vita00w"]:
        print(f"错误：不支持的机器人类型 {args.robot}")
        return

    create_test_scene(args.robot)


if __name__ == "__main__":
    main()
