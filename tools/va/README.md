# va

> **V**ita data **A**ssistant

A CLI tools to assist vita data.

## Install

### uv

macos:

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

linux:

```bash
wget -qO- https://astral.sh/uv/install.sh | sh
```

```bash
uv sync
uv run va xx.mcap
```

## Usage

### Print info

```bash
uv run va xx.mcap

Start: 2025-02-11 21:04:50.291 (1739279090.291503010)
End  : 2025-02-11 21:05:00.220 (1739279100.220801584)

+-----------+----------+-------------------------+-----------------+
| Topic     |   Number | Schema                  | Serialization   |
+===========+==========+=========================+=================+
| /lowcmd   |     4946 | unitree_go/msg/LowCmd   | cdr             |
+-----------+----------+-------------------------+-----------------+
| /lowstate |     4964 | unitree_go/msg/LowState | cdr             |
+-----------+----------+-------------------------+-----------------+
```

### Find message

```bash
uv run va xx.mcap --topic /lowcmd --ts 1739279090.291503010
```

### Advanced

按照 `topic` 和 `timestamp` 查找消息，返回的一定是一个 `json`。所以可以接 `jq` 这样的工具进一步筛选。

```bash
sudo apt install jq -y
```

Example:

```bash
uv run va xx.mcap --topic /lowcmd --ts 1739279090.291503010 | jq '.motor_cmd[].q'
```

会输出如下:

```bash
0.08073611557483673
0.888074517250061
-1.532440185546875
0.019166702404618263
0.9356516599655151
-1.515163779258728
0.07157745212316513
1.1827611923217773
-1.7613203525543213
-0.028237534686923027
1.2574893236160278
-1.8067147731781006
2146000000
2146000000
2146000000
2146000000
2146000000
2146000000
2146000000
2146000000
```

### Modify message

```bash
uv run va xx.mcap --topic /lowcmd --ts 1739279090.291503010 --set 'motor_cmd[0].q=0.1'
```

会生成一个新的 `xx_modified.mcap` 文件，那一帧的消息字段已经被修改。

### UI

```bash
uv run va xx.mcap --ui
```

会打开一个 web 页面，可以查看和修改消息。
