"""MCAP 文件查看和修改工具"""

import sys
import threading
import time

from .mcap_utils import McapInfo, MessageFinder
from .cli import parse_args
from .ui import WebUI


def main() -> None:
    """主函数"""
    cmd = parse_args()

    if cmd.info:
        # 显示文件信息
        info = McapInfo(cmd.info.file_path)
        info.print_info()

    elif cmd.find:
        # 查找消息
        finder = MessageFinder(cmd.find.file_path)
        finder.find_message(cmd.find.topic, cmd.find.timestamp)

    elif cmd.modify:
        # 修改消息
        finder = MessageFinder(cmd.modify.file_path)
        finder.modify_message(
            cmd.modify.topic, cmd.modify.timestamp, cmd.modify.setters
        )

    elif cmd.ui:
        print(f"Web UI running at: http://localhost:8080")

        ui = WebUI(cmd.ui.file_path)
        ui_thread = threading.Thread(target=ui.run, daemon=True)
        ui_thread.start()

        try:
            while ui_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
        finally:
            print("UI closed")

    else:
        print("Usage:", file=sys.stderr)
        print("  1. Show MCAP info:", file=sys.stderr)
        print("     va <mcap_file>", file=sys.stderr)
        print("  2. Find message by topic and timestamp:", file=sys.stderr)
        print(
            "     va <mcap_file> --topic <topic_name> --ts <timestamp.nanoseconds>",
            file=sys.stderr,
        )
        print("  3. Modify message field:", file=sys.stderr)
        print(
            "     va <mcap_file> --topic <topic_name> --ts <timestamp.nanoseconds> --set 'field[index]=value'",
            file=sys.stderr,
        )
        print("  4. Launch Web UI:", file=sys.stderr)
        print(
            "     va <mcap_file> --ui",
            file=sys.stderr,
        )
        print("\nExamples:", file=sys.stderr)
        print(
            "  va data.mcap --topic /joint_states --ts 1234.567890 --set 'motor_cmd[1].q =0.98'",
            file=sys.stderr,
        )
        sys.exit(1)
