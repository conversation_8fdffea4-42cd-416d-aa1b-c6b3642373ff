"""命令行解析模块"""

from dataclasses import dataclass
from typing import List, Optional
import argparse

from .field_path import FieldSetter


@dataclass
class FindCommand:
    """查找命令参数"""

    file_path: str
    topic: str
    timestamp: str


@dataclass
class ModifyCommand:
    """修改命令参数"""

    file_path: str
    topic: str
    timestamp: str
    setters: List[FieldSetter]


@dataclass
class InfoCommand:
    """信息命令参数"""

    file_path: str


@dataclass
class UICommand:
    """UI命令参数"""
    
    file_path: str


class Command:
    """命令解析结果"""

    def __init__(self):
        self.info: Optional[InfoCommand] = None
        self.find: Optional[FindCommand] = None
        self.modify: Optional[ModifyCommand] = None
        self.ui: Optional[UICommand] = None


def parse_args() -> Command:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Vita Assistant",
        formatter_class=argparse.RawTextHelpFormatter,
    )

    parser.add_argument("file", help="mcap file path")
    parser.add_argument("--topic", help="topic name")
    parser.add_argument("--ts", help="timestamp (sec.nanosec)")
    parser.add_argument(
        "--set",
        action="append",
        help="set field value, format: field.path[index]=value",
    )
    parser.add_argument(
        "--ui",
        action="store_true",
        help="start web UI interface",
    )

    args = parser.parse_args()
    cmd = Command()

    if args.ui:
        cmd.ui = UICommand(args.file)
    elif args.topic is None and args.ts is None and not args.set:
        cmd.info = InfoCommand(args.file)
    elif args.topic and args.ts:
        if args.set:
            cmd.modify = ModifyCommand(
                file_path=args.file,
                topic=args.topic,
                timestamp=args.ts,
                setters=[FieldSetter(expr) for expr in args.set],
            )
        else:
            cmd.find = FindCommand(
                file_path=args.file, topic=args.topic, timestamp=args.ts
            )
    else:
        parser.error("missing required arguments")

    return cmd
