"""MCAP 消息查找工具"""

from mcap.reader import make_reader
from mcap.writer import Writer as McapWriter
from rich.console import Console
from rich.json import JSON
import json
from typing import List, Tuple

from rclpy.serialization import deserialize_message, serialize_message
from rosidl_runtime_py.utilities import get_message
from rosidl_runtime_py import message_to_ordereddict

from .ros_utils import (
    dynamic_dict_to_msg,
    set_nested_value,
    generate_incremental_filename,
)


def print_json(data):
    """以美观的方式打印 JSON 数据"""
    console = Console()
    console.print(JSON(json.dumps(data)))


class MessageFinder:
    """MCAP 消息查找类"""

    def __init__(self, file_path):
        self.file_path = file_path
        self.reader = None

    def parse_timestamp(self, timestamp):
        """解析时间戳字符串为纳秒"""
        seconds, nanoseconds = str(timestamp).split(".")
        return int(seconds) * 1_000_000_000 + int(f"{nanoseconds:0<9}"[:9])

    def format_timestamp(self, timestamp_ns):
        """格式化纳秒时间戳"""
        return f"{timestamp_ns // 1_000_000_000}.{timestamp_ns % 1_000_000_000:09d}"

    def find_message(self, topic, timestamp):
        """查找指定 topic 和时间点的消息"""
        logtime_ns = self.parse_timestamp(timestamp)
        WINDOW_SIZE = 100_000_000  # 100ms = 1e8纳秒

        with open(self.file_path, "rb") as f:
            reader = make_reader(f)
            summary = reader.get_summary()

            # 获取消息类型
            schema_name = None
            for channel in summary.channels.values():
                if channel.topic == topic:
                    schema_name = summary.schemas[channel.schema_id].name
                    break

            if not schema_name:
                print_json({})
                return

            # 使用时间窗口查找最近的消息
            closest_message = None
            closest_time_diff = float("inf")

            # 在100ms时间窗口内查找
            messages = list(
                reader.iter_messages(
                    topics=[topic],
                    start_time=logtime_ns - WINDOW_SIZE,
                    end_time=logtime_ns + WINDOW_SIZE,
                )
            )

            if messages:
                # 在获取到的消息中找最近的
                for _, channel, message in messages:
                    time_diff = abs(message.log_time - logtime_ns)
                    if time_diff < closest_time_diff:
                        closest_time_diff = time_diff
                        closest_message = message

                if closest_message:
                    msg_type = get_message(schema_name)
                    msg = deserialize_message(closest_message.data, msg_type)
                    msg_dict = message_to_ordereddict(msg)
                    print_json(msg_dict)
            else:
                print_json({})

    def modify_message(
        self, topic: str, timestamp: float, field_setters: List[Tuple[List[str], float]]
    ) -> str:
        """修改指定消息的字段并重写文件"""

        logtime_ns = self.parse_timestamp(timestamp)
        modified_file = generate_incremental_filename(self.file_path)

        with open(self.file_path, "rb") as input_stream:
            reader = make_reader(input_stream)

            with open(modified_file, "wb") as output_stream:
                writer = McapWriter(output_stream)
                writer.start()

                summary = reader.get_summary()
                schema_map = {}
                for schema in summary.schemas.values():
                    new_schema_id = writer.register_schema(
                        name=schema.name, encoding=schema.encoding, data=schema.data
                    )
                    schema_map[schema.id] = new_schema_id

                channel_map = {}
                for channel in summary.channels.values():
                    new_channel_id = writer.register_channel(
                        schema_id=schema_map.get(channel.schema_id),
                        topic=channel.topic,
                        message_encoding=channel.message_encoding,
                        metadata=channel.metadata,
                    )
                    channel_map[channel.id] = new_channel_id

                for schema, channel, message in reader.iter_messages():
                    message_data = message.data

                    if channel.topic == topic and message.log_time == logtime_ns:

                        msg_type = get_message(schema.name)
                        data = deserialize_message(message.data, msg_type)
                        msg_dict = message_to_ordereddict(data)

                        for path, value in field_setters:
                            set_nested_value(msg_dict, path, value)

                        modified_msg = dynamic_dict_to_msg(msg_dict, msg_type)
                        message_data = serialize_message(modified_msg)

                    writer.add_message(
                        channel_id=channel_map[channel.id],
                        log_time=message.log_time,
                        data=message_data,
                        publish_time=message.publish_time,
                    )

                writer.finish()

        print(f"Modified file saved to {modified_file}")
        return modified_file
