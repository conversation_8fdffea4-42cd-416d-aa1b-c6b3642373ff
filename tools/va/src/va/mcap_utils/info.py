"""MCAP 文件信息处理"""

from datetime import datetime
from mcap.reader import make_reader
from tabulate import tabulate


class McapInfo:
    """MCAP 文件信息处理类"""

    def __init__(self, file_path):
        self.file_path = file_path
        self.reader = None
        self.summary = None

    def load(self):
        """加载 MCAP 文件"""
        with open(self.file_path, "rb") as f:
            self.reader = make_reader(f)
            self.summary = self.reader.get_summary()

    def format_timestamp(self, timestamp_ns):
        """格式化时间戳"""
        time = datetime.fromtimestamp(timestamp_ns / 1e9)
        time_format = "%Y-%m-%d %H:%M:%S.%f"
        ts = f"{timestamp_ns // 1_000_000_000}.{timestamp_ns % 1_000_000_000:09d}"
        return time.strftime(time_format)[:-3], ts

    def print_info(self):
        """打印 MCAP 文件信息"""
        if not self.summary:
            self.load()

        # 打印时间范围
        start_time, start_ts = self.format_timestamp(self.summary.statistics.message_start_time)
        end_time, end_ts = self.format_timestamp(self.summary.statistics.message_end_time)

        print(f"Start: {start_time:<23} ({start_ts:>20})")
        print(f"End  : {end_time:<23} ({end_ts:>20})")
        print()

        # 打印通道信息
        table_data = []
        for channel in self.summary.channels.values():
            table_data.append(
                [
                    channel.topic,
                    self.summary.statistics.channel_message_counts[channel.id],
                    self.summary.schemas[channel.schema_id].name,
                    channel.message_encoding,
                ]
            )

        headers = ["Topic", "Number", "Schema", "Serialization"]
        print(tabulate(table_data, headers=headers, tablefmt="grid")) 