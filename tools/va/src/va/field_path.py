"""字段路径解析模块"""

from typing import List, Any, Iterator
import ast


class FieldPath:
    """字段路径解析器"""

    def __init__(self, path_str: str):
        self.original = path_str
        self.components = self._parse(path_str)

    def _parse(self, path_str: str) -> List[str]:
        """解析字段路径字符串，返回路径组件列表"""
        # 替换方括号为点号，方便分割
        normalized = path_str.replace("[", ".").replace("]", "")
        return [comp for comp in normalized.split(".") if comp]

    def get_value(self, data: Any) -> Any:
        """获取字段值"""
        current = data
        for comp in self.components:
            if isinstance(current, dict):
                current = current[comp]
            elif isinstance(current, (list, tuple)) and comp.isdigit():
                current = current[int(comp)]
            else:
                current = getattr(current, comp)
        return current

    def set_value(self, data: Any, value: Any) -> None:
        """设置字段值"""
        current = data
        for comp in self.components[:-1]:
            if isinstance(current, dict):
                if comp not in current:
                    current[comp] = {}
                current = current[comp]
            elif isinstance(current, (list, tuple)) and comp.isdigit():
                idx = int(comp)
                if len(current) <= idx:
                    # 如果是列表且索引超出范围，扩展列表
                    current.extend([{} for _ in range(idx - len(current) + 1)])
                current = current[idx]
            else:
                if not hasattr(current, comp):
                    setattr(current, comp, {})
                current = getattr(current, comp)

        last = self.components[-1]
        if isinstance(current, dict):
            current[last] = value
        elif isinstance(current, list) and last.isdigit():
            current[int(last)] = value
        else:
            setattr(current, last, value)

    def __str__(self) -> str:
        return ".".join(self.components)


class FieldSetter:
    """字段设置器"""

    def __init__(self, expr_or_path, value=None):
        """初始化字段设置器

        Args:
            expr_or_path: 可以是设置表达式字符串(如 'motor_cmd[1].q=0.98'),
                          或者是路径组件列表(如 ['motor_cmd', '1', 'q'])
            value: 当 expr_or_path 是路径列表时的设置值
        """
        if isinstance(expr_or_path, str):
            # 原有的字符串表达式解析逻辑
            path_str, value_str = expr_or_path.split("=", 1)
            field_path = FieldPath(path_str.strip())
            # 尝试解析值为合适的 Python 类型
            try:
                value = ast.literal_eval(value_str.strip())
            except (ValueError, SyntaxError):
                # 如果解析失败，保持原始字符串
                value = value_str.strip()

            self.path = field_path.components
            self.value = value
            self._field_path = field_path
        else:
            # 新增的路径列表处理逻辑
            self.path = list(expr_or_path)  # 确保是列表
            self.value = value
            # 构造等效的字段路径
            path_str = ""
            for i, comp in enumerate(self.path):
                if comp.isdigit() and i > 0:  # 数字且不是第一个组件
                    path_str += f"[{comp}]"
                else:
                    if i > 0:
                        path_str += "."
                    path_str += comp
            self._field_path = FieldPath(path_str)

    def apply(self, data: Any) -> None:
        """应用修改"""
        self._field_path.set_value(data, self.value)

    def __str__(self) -> str:
        return f"{'.'.join(self.path)}={self.value}"

    def __iter__(self) -> Iterator[Any]:
        """使对象可迭代，返回 path 和 value"""
        yield self.path
        yield self.value
