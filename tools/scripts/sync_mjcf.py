import xml.etree.ElementTree as ET
import xml.dom.minidom
import re
from typing import Optional, Dict
import subprocess
import argparse
import os
import sys


def create_default_block() -> ET.Element:
    """Create the default block XML structure with visual and collision settings."""
    return ET.fromstring(
        """
    <default>
      <default class="vita">
        <geom friction="0.4" margin="0.001" condim="3"/>
        <joint axis="0 1 0" damping="0.1" armature="0.0043" frictionloss="0.2"/>
        <motor forcerange="-17 17"/>
        <default class="R_hip_joint">
          <joint axis="1 0 0" range="-0.785 0.785" actuatorfrcrange="-17 17" limited="true"/>
        </default>
        <default class="L_hip_joint">
          <joint axis="1 0 0" range="-0.785 0.785" actuatorfrcrange="-17 17" limited="true"/>
        </default>
        <default class="F_thigh_joint">
          <joint range="-1.81 3.05" actuatorfrcrange="-17 17" limited="true"/>
        </default>
        <default class="R_thigh_joint">
          <joint range="-0.75 4.13" actuatorfrcrange="-17 17" limited="true"/>
        </default>
        <default class="calf_joint">
          <joint range="-2.757 -0.89" actuatorfrcrange="-34 34" limited="true" damping="0.1" armature="0.04" frictionloss="0.2"/>
          <motor forcerange="-34 34"/>
        </default>
        <default class="visual">
          <geom type="mesh" contype="0" conaffinity="0" group="2" density="0"/>
        </default>
        <default class="collision">
          <geom group="3" rgba="0.752941 0.752941 0.752941 1"/>
          <default class="foot">
            <geom size="0.021" quat="0.707107 -0.707107 0 0" pos="0 0 0"/>
          </default>
        </default>
      </default>
    </default>
    """
    )


def extract_joint_ranges_from_urdf(urdf_path: str) -> Dict[str, str]:
    """Extract joint ranges from URDF file for different joint types."""
    ranges: Dict[str, Optional[str]] = {
        "R_hip_range": None,
        "L_hip_range": None,
        "F_thigh_range": None,
        "R_thigh_range": None,
        "calf_range": None,
    }

    urdf_tree = ET.parse(urdf_path)
    urdf_root = urdf_tree.getroot()
    
    for joint in urdf_root.findall(".//joint"):
        name = joint.get("name", "")
        range_str = joint.find("limit")
        if range_str is None:
            continue
        range_val = range_str.get("lower", "") + " " + range_str.get("upper", "")
        if not range_val:
            continue

        if name.endswith("hip_joint"):
            if any(name.startswith(prefix) for prefix in ["R", "FR", "RR"]):
                ranges["R_hip_range"] = range_val
            elif any(name.startswith(prefix) for prefix in ["L", "FL", "RL"]):
                ranges["L_hip_range"] = range_val
        elif name.endswith("thigh_joint"):
            if any(name.startswith(prefix) for prefix in ["F", "FR", "FL"]):
                ranges["F_thigh_range"] = range_val
            elif any(name.startswith(prefix) for prefix in ["R", "RR", "RL"]):
                ranges["R_thigh_range"] = range_val
        elif name.endswith("calf_joint"):
            ranges["calf_range"] = range_val

    return ranges


def update_default_block(default_block: ET.Element, ranges: Dict[str, str]) -> None:
    """Update default block with extracted joint ranges."""
    joint_configs = [
        ("R_hip_joint", "R_hip_range"),
        ("L_hip_joint", "L_hip_range"),
        ("F_thigh_joint", "F_thigh_range"),
        ("R_thigh_joint", "R_thigh_range"),
        ("calf_joint", "calf_range"),
    ]

    for cls_name, range_key in joint_configs:
        target = default_block.find(f'.//default[@class="{cls_name}"]/joint')
        if target is not None and ranges[range_key]:
            target.set("range", ranges[range_key])


def restructure_worldbody(root: ET.Element, urdf_path: str) -> None:
    """Restructure worldbody by wrapping geoms and leg bodies under base."""
    worldbody = root.find("worldbody")
    if worldbody is None:
        worldbody = ET.SubElement(root, "worldbody")

    # fixed height, should be at least higher than com height at status "stand"
    base_body = ET.SubElement(
        worldbody, "body", name="base", pos="0 0 0.462", childclass="vita"
    )

    base_pos, base_mass, base_fullinertia = get_base_inertial_data(urdf_path)

    # Add inertial components
    ET.SubElement(
        base_body,
        "inertial",
        pos=base_pos,
        mass=base_mass,
        fullinertia=base_fullinertia,
    )
    ET.SubElement(base_body, "freejoint", name="joint_fixed_world")
    ET.SubElement(base_body, "site", name="trunk_imu", pos="0 0 0")

    # Move existing geoms into base_body
    for geom in list(worldbody.findall("geom")):
        worldbody.remove(geom)
        base_body.append(geom)

    # Move leg bodies into base_body
    leg_names = ["FR_hip", "FL_hip", "RR_hip", "RL_hip"]
    for leg_name in leg_names:
        if (leg_body := worldbody.find(f'body[@name="{leg_name}"]')) is None:
            continue
        worldbody.remove(leg_body)
        base_body.append(leg_body)

        # 添加足端body到calf_joint子树下
        prefix = leg_name.split("_")[0]
        calf_joint_name = f"{prefix}_calf_joint"
        for body in leg_body.iter("body"):
            joint = body.find(f'joint[@name="{calf_joint_name}"]')
            if joint is None:
                continue
            foot_pos = "0 0 0"
            # 删除原有球形碰撞体（size为单个数值的视为球体）
            for geom in list(body):
                if geom.tag != "geom":
                    continue
                size_values = geom.get("size", "").split()
                if len(size_values) != 1:
                    continue
                body.remove(geom)
                foot_pos = geom.get("pos", foot_pos)
            foot_name = f"{prefix}_foot"
            foot_body = ET.SubElement(body, "body", name=foot_name, pos=foot_pos)
            ET.SubElement(foot_body, "geom", name=prefix, attrib={"class": "foot"})
            break


def update_joints_and_geoms(base_body: ET.Element) -> None:
    """Update joint and geom attributes using class inheritance."""
    attrs_to_remove = ["pos", "axis", "range", "actuatorfrcrange", "limited"]
    joint_configs = [
        ("hip", "R_hip_joint", "L_hip_joint"),
        ("thigh", "F_thigh_joint", "R_thigh_joint"),
        ("calf", "calf_joint", None),
    ]

    for suffix, r_class, l_class in joint_configs:
        for joint in base_body.findall(f".//joint"):
            name = joint.get("name", "")
            if suffix in name:
                if suffix == "hip":
                    cls = l_class if "L" in name else r_class
                elif suffix == "thigh":
                    cls = r_class if name.startswith("F") else l_class
                else:  # calf
                    cls = r_class

                joint.set("class", cls)
                for attr in attrs_to_remove:
                    joint.attrib.pop(attr, None)

    for geom in base_body.findall(".//geom"):
        if "class" in geom.attrib:
            continue
        if geom.get("type") == "mesh":
            geom.set("class", "visual")
            for attr in ["contype", "conaffinity", "density", "group"]:
                geom.attrib.pop(attr, None)
        else:
            geom.set("class", "collision")
            for attr in ["rgba", "group"]:
                geom.attrib.pop(attr, None)


def add_actuators_and_sensors(root: ET.Element) -> None:
    """Add actuator and sensor blocks with improved code reuse."""
    legs = ["FR", "FL", "RR", "RL"]
    leg_comments = {
        "FR": " FR - 前右 ",
        "FL": " FL - 前左 ",
        "RR": " RR - 后右 ",
        "RL": " RL - 后左 ",
    }

    # Add actuators
    if root.find("actuator") is None:
        actuator = ET.SubElement(root, "actuator")
        for leg in legs:
            actuator.append(ET.Comment(leg_comments[leg]))
            for joint in ["hip", "thigh", "calf"]:
                motor_name = f"{leg}_{joint}_joint"
                motor_class = "calf_joint" if joint == "calf" else "vita"
                ET.SubElement(
                    actuator,
                    "motor",
                    name=motor_name,
                    joint=motor_name,
                    attrib={"class": motor_class},
                )

    # Define sensor configurations
    sensor_configs = [
        ("jointpos", "_pos", {}),
        ("jointvel", "_vel", {}),
        ("jointactuatorfrc", "_torque", {"noise": "0.01"}),
    ]

    # Add sensors
    if root.find("sensor") is None:
        sensor = ET.SubElement(root, "sensor")

        # Add joint sensors (position, velocity, torque)
        sensor_types = {
            "jointpos": " 关节位置传感器 - 按照与执行器相同的顺序排列 ",
            "jointvel": " 关节速度传感器 - 按照与执行器相同的顺序排列 ",
            "jointactuatorfrc": " 关节力矩传感器 - 按照与执行器相同的顺序排列 ",
        }

        for sensor_type, comment in sensor_types.items():
            sensor.append(ET.Comment(comment))
            for leg in legs:
                sensor.append(ET.Comment(leg_comments[leg]))
                for joint in ["hip", "thigh", "calf"]:
                    jname = f"{leg}_{joint}_joint"
                    sensor_element = sensor_configs[
                        [s[0] for s in sensor_configs].index(sensor_type)
                    ]
                    ET.SubElement(
                        sensor,
                        sensor_element[0],
                        name=f"{jname}{sensor_element[1]}",
                        joint=jname,
                        **sensor_element[2],
                    )

        # Add IMU sensors
        sensor.append(ET.Comment(" IMU传感器 "))
        ET.SubElement(
            sensor, "framequat", name="trunk_quat", objtype="site", objname="trunk_imu"
        )
        ET.SubElement(sensor, "gyro", name="trunk_gyro", site="trunk_imu")
        ET.SubElement(sensor, "accelerometer", name="trunk_acc", site="trunk_imu")
        sensor.append(ET.Comment(" 位置和速度传感器 "))
        ET.SubElement(
            sensor, "framepos", name="trunk_pos", objtype="site", objname="trunk_imu"
        )
        ET.SubElement(
            sensor, "framelinvel", name="trunk_vel", objtype="site", objname="trunk_imu"
        )


def get_base_inertial_data(urdf_path: str) -> tuple:
    """Extract inertial data from URDF file for base link"""
    # Parse URDF to extract inertial data
    urdf_tree = ET.parse(urdf_path)
    urdf_root = urdf_tree.getroot()
    base_link = urdf_root.find(".//link[@name='base']")

    base_pos = "0 0 0"
    base_mass = "0"
    base_fullinertia = "0 0 0 0 0 0"

    if base_link is not None:
        inertial = base_link.find("inertial")
        if inertial is not None:
            mass_elem = inertial.find("mass")
            if mass_elem is not None:
                base_mass = mass_elem.get("value", "0")
            origin = inertial.find("origin")
            if origin is not None:
                base_pos = origin.get("xyz", "0 0 0")
            inertia = inertial.find("inertia")
            if inertia is not None:
                fullinertia_values = [
                    inertia.get("ixx", "0"),
                    inertia.get("iyy", "0"),
                    inertia.get("izz", "0"),
                    inertia.get("ixy", "0"),
                    inertia.get("ixz", "0"),
                    inertia.get("iyz", "0"),
                ]
                base_fullinertia = " ".join(fullinertia_values)

    return base_pos, base_mass, base_fullinertia


def check_symmetry(left_body: ET.Element, right_body: ET.Element) -> None:
    """Check symmetry between two bodies' geom elements."""
    left_geoms = left_body.findall(".//geom")
    right_geoms = right_body.findall(".//geom")
    
    for i, (lg, rg) in enumerate(zip(left_geoms, right_geoms)):
        # Check size equality
        if lg.get("size") != rg.get("size"):
            print(f"[Symmetry Check] Size mismatch at {i} between {left_body.get('name')} and {right_body.get('name')}")
            print(f"  Left geom: {ET.tostring(lg).decode()}")
            print(f"  Right geom: {ET.tostring(rg).decode()}")
            
        # Check y-axis position symmetry
        lg_pos = lg.get("pos", "0 0 0").split()
        rg_pos = rg.get("pos", "0 0 0").split()
        
        if len(lg_pos) >= 2 and len(rg_pos) >= 2:
            try:
                lg_y = float(lg_pos[1])
                rg_y = float(rg_pos[1])
                if abs(lg_y + rg_y) > 1e-6:  # Allowing floating point tolerance
                    print(f"[Symmetry Check] Y-position asymmetry at {i} between {left_body.get('name')} and {right_body.get('name')}")
                    print(f"  Left geom: {ET.tostring(lg).decode()}")
                    print(f"  Right geom: {ET.tostring(rg).decode()}")
            except ValueError:
                pass  # Skip non-numeric values


def check_same_side_equality(left_body: ET.Element, right_body: ET.Element) -> None:
    """Check that corresponding geom elements on the same side have equal values."""
    left_geoms = left_body.findall(".//geom")
    right_geoms = right_body.findall(".//geom")

    for i, (lg, rg) in enumerate(zip(left_geoms, right_geoms)):
        # Check size equality
        if lg.get("size") != rg.get("size"):
            print(f"[Same Side Check] Size mismatch at {i} between {left_body.get('name')} and {right_body.get('name')}")
            print(f"  Left geom: {ET.tostring(lg).decode()}")
            print(f"  Right geom: {ET.tostring(rg).decode()}")

        # Check x and z position equality
        lg_pos = lg.get("pos", "0 0 0").split()
        rg_pos = rg.get("pos", "0 0 0").split()

        if len(lg_pos) >= 3 and len(rg_pos) >= 3:
            try:
                lg_x, lg_z = float(lg_pos[0]), float(lg_pos[2])
                rg_x, rg_z = float(rg_pos[0]), float(rg_pos[2])
                if abs(lg_x - rg_x) > 1e-6 or abs(lg_z - rg_z) > 1e-6:
                    print(f"[Same Side Check] X or Z position mismatch at {i} between {left_body.get('name')} and {right_body.get('name')}")
                    print(f"  Left geom: {ET.tostring(lg).decode()}")
                    print(f"  Right geom: {ET.tostring(rg).decode()}")
            except ValueError:
                pass  # Skip non-numeric values


def convert_vita_xml(bak_file: str, output_file: str, urdf_path: str) -> None:
    """Convert Vita XML to MuJoCo format with standardized structure."""

    tree = ET.parse(bak_file)
    root = tree.getroot()
    index = 0

    # 1. Update compiler
    if (compiler := root.find("compiler")) is not None:
        compiler.set("meshdir", "meshes/")
    else:
        ET.SubElement(root, "compiler", angle="radian", meshdir="meshes/")

    # 2. Add option after compiler
    compiler = root.find("compiler")  # Find after any modifications
    if root.find("option") is None and compiler is not None:
        index = list(root).index(compiler)
        index += 1
        root.insert(index, ET.Element("option", cone="elliptic", impratio="100"))

    # 3. Create and configure default block
    default_block = create_default_block()
    index += 1
    root.insert(index, default_block)

    ranges = extract_joint_ranges_from_urdf(urdf_path)
    update_default_block(default_block, ranges)
    # 4. Restructure worldbody
    restructure_worldbody(root, urdf_path)

    # 5. Update joints and geoms
    if (worldbody := root.find("worldbody")) is not None:
        base_body = worldbody.find('body[@name="base"]')
        if base_body is not None:
            update_joints_and_geoms(base_body)
            
            # Symmetry validation for hip joints
            fl_hip = base_body.find('.//body[@name="FL_hip"]')
            fr_hip = base_body.find('.//body[@name="FR_hip"]')
            rl_hip = base_body.find('.//body[@name="RL_hip"]')
            rr_hip = base_body.find('.//body[@name="RR_hip"]')

            if fl_hip is not None and fr_hip is not None:
                check_symmetry(fl_hip, fr_hip)
            if rl_hip is not None and rr_hip is not None:
                check_symmetry(rl_hip, rr_hip)

            # Same-side validation
            if fl_hip is not None and rl_hip is not None:
                check_same_side_equality(fl_hip, rl_hip)
            if fr_hip is not None and rr_hip is not None:
                check_same_side_equality(fr_hip, rr_hip)

    # 6. Add actuators and sensors
    add_actuators_and_sensors(root)

    # 7. Save the updated XML with pretty formatting
    xml_str = ET.tostring(root, encoding="utf-8")
    dom = xml.dom.minidom.parseString(xml_str)
    pretty_xml_str = dom.toprettyxml(indent="  ")

    # Remove extra blank lines
    pretty_xml_str = re.sub(r"(\n\s*)+\n", "\n", pretty_xml_str).strip()

    with open(output_file, "w", encoding="utf-8") as f:
        f.write(pretty_xml_str)


def main():
    parser = argparse.ArgumentParser(description="Process URDF and generate XML.")
    parser.add_argument(
        "-u", "--urdf_path", required=True, help="Path to the input URDF file."
    )
    parser.add_argument(
        "-x", "--xml_path", required=True, help="Path to the output XML file."
    )
    args = parser.parse_args()

    # Generate corresponding bak file path
    bak_file_path = os.path.join(os.path.dirname(args.xml_path), "vita02_bak.xml")

    commands = [
        "source install/setup.bash",
        f"compile {args.urdf_path} {bak_file_path}",
    ]

    # Execute ROS compilation pipeline
    process = subprocess.Popen(
        ["bash", "-c", " && ".join(commands)],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    if os.path.exists(bak_file_path):
        os.remove(bak_file_path)
    stdout, stderr = process.communicate()

    # 检查bak文件是否生成，否则输出日志并退出
    if not os.path.exists(bak_file_path):
        print(f"Error: Failed to generate bak file. Output: {stdout}")
        if stderr:
            print(f"Error output: {stderr}")
        if stdout:
            print(f"MuJoCo compilation warnings: {stdout}")
        sys.exit(1)

    # Run XML conversion pipeline
    convert_vita_xml(bak_file_path, args.xml_path, args.urdf_path)

    # Delete intermediate bak file after successful conversion
    os.remove(bak_file_path)


if __name__ == "__main__":
    main()
