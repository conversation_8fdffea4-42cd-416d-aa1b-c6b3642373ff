# macOS 使用指南

## 问题说明

在 macOS 上，ROS2 消息包生成的 Python 扩展模块无法正确找到动态库文件（`.dylib`），导致运行时出现 `ImportError` 错误。

## 解决方案

使用提供的脚本创建符号链接，让系统能正确找到动态库：

```bash
# 1. 编译消息包
make msg

# 2. 设置动态库链接
./tools/macos/setup_dylib_links.sh

# 3. 正常使用
source install/setup.zsh
make himloco_mujoco
```

## 何时需要运行脚本

- 首次编译后
- 清理 `build/` 和 `install/` 目录后重新编译时
- 动态库更新后

## 原理

该脚本在 conda 环境的 `lib` 目录中创建指向项目动态库的符号链接，解决 macOS 动态链接器的路径查找问题。

## 注意事项

- 此解决方案仅适用于 macOS
- 需要确保 conda ros2 环境已正确安装
- 符号链接会在环境切换后保持有效 