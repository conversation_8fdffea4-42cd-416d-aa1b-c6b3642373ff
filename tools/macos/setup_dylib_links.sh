#!/bin/bash

# macOS 动态库符号链接设置脚本
# 解决 ROS2 消息包在 macOS 上的动态库加载问题

set -e

echo "🔗 设置 macOS 动态库符号链接..."

# 检查是否在 macOS 上运行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ 此脚本仅适用于 macOS 系统"
    exit 1
fi

# 检查 conda 环境
CONDA_LIB_DIR="$HOME/miniconda3/envs/ros2/lib"
if [ ! -d "$CONDA_LIB_DIR" ]; then
    echo "❌ 未找到 conda ros2 环境: $CONDA_LIB_DIR"
    exit 1
fi

# 检查必要的目录
if [ ! -d "install/lowlevel_msg/lib" ]; then
    echo "❌ 未找到 install/lowlevel_msg/lib 目录，请先运行 make msg"
    exit 1
fi

if [ ! -d "install/him_pkg/lib" ]; then
    echo "⚠️  install/him_pkg/lib 不存在，跳过 him_pkg 链接"
    HIM_PKG_EXISTS=false
else
    HIM_PKG_EXISTS=true
fi

echo "📁 目标目录: $CONDA_LIB_DIR"
echo ""

# 创建 lowlevel_msg 符号链接
echo "🔗 创建 lowlevel_msg 动态库链接..."
for dylib in install/lowlevel_msg/lib/*.dylib; do
    if [ -f "$dylib" ]; then
        lib_name=$(basename "$dylib")
        ln -sf "$PWD/$dylib" "$CONDA_LIB_DIR/$lib_name"
        echo "   ✅ $lib_name"
    fi
done

# 创建 unitree_go 符号链接
echo "🔗 创建 unitree_go 动态库链接..."
for dylib in install/unitree_go/lib/*.dylib; do
    if [ -f "$dylib" ]; then
        lib_name=$(basename "$dylib")
        ln -sf "$PWD/$dylib" "$CONDA_LIB_DIR/$lib_name"
        echo "   ✅ $lib_name"
    fi
done

# 创建 him_pkg 符号链接（如果存在）
if [ "$HIM_PKG_EXISTS" = true ]; then
    echo "🔗 创建 him_pkg 动态库链接..."
    for dylib in install/him_pkg/lib/*.dylib; do
        if [ -f "$dylib" ]; then
            lib_name=$(basename "$dylib")
            ln -sf "$PWD/$dylib" "$CONDA_LIB_DIR/$lib_name"
            echo "   ✅ $lib_name"
        fi
    done
fi

echo ""
echo "🎉 符号链接设置完成！"
echo ""
echo "💡 现在可以正常运行："
echo "   source install/setup.zsh"
echo "   make himloco_mujoco" 