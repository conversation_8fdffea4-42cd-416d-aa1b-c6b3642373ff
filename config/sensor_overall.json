{"configById": {"DogLegSideView.DogLegSideView!4cpw19t": {"robotType": "vita00", "fullRangePitch": false, "lowStateTopic": "/lowstate", "lowCmdTopic": "/lowcmd"}, "Plot!wd01tt": {"paths": [{"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.position.y", "enabled": true, "color": "#4e98e2", "xValuePath": "/rt/odom.pose.pose.position.x", "showLine": true, "label": "Pos"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "custom", "sidebarDimension": 240, "axisScalesMode": "lockedScales", "foxglovePanelTitle": "<PERSON><PERSON>"}, "Plot!31we15j": {"paths": [{"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.orientation.w", "enabled": true, "color": "#4e98e2", "label": "quat.w"}, {"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.orientation.x", "enabled": true, "color": "#f5774d", "label": "quat.x"}, {"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.orientation.y", "enabled": true, "color": "#f7df71", "label": "quat.y"}, {"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.orientation.z", "enabled": true, "color": "#5cd6a9", "label": "quat.z"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "RawMessages!1ybnn1v": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/odom", "fontSize": 12}, "NodePlayground!ewybt2": {"selectedNodeId": "f4f5cbf3-1c58-4e03-b2f0-4d85075e8215", "autoFormatOnSave": true}, "3D!1sm58o5": {"cameraState": {"perspective": false, "distance": 11.376001845521222, "phi": 42.34716229879335, "thetaOffset": -71.71213351897732, "targetOffset": [-0.7496308228196703, 0.3668601484419261, -0.32501163031829633], "target": [0, 0, 0], "targetOrientation": [0, 0, 0, 1], "fovy": 45, "near": 0.5, "far": 5000}, "followMode": "follow-none", "followTf": "hesai_lidar", "scene": {}, "transforms": {"frame:": {"visible": false}, "frame:default_cam": {"visible": false}, "frame:hesai_lidar": {"visible": true}, "frame:uwb": {"visible": false}, "frame:odom": {"visible": false}}, "topics": {"/image_left_raw/h264": {"visible": true, "frameLocked": true, "cameraInfoTopic": "/image_left_raw/camera_info", "distance": 1, "planarProjectionFactor": 0, "color": "#ffffff"}, "/lidar_points": {"visible": true, "colorField": "intensity", "colorMode": "colormap", "colorMap": "turbo", "pointShape": "cube", "cubeSize": 0.07, "cubeOutline": true}, "/vis/uwb": {"visible": true}, "/uwb/poses": {"visible": true, "axisScale": 0.5}, "/uwb/processed/poses": {"visible": true, "type": "axis", "lineWidth": 1, "axisScale": 1}}, "layers": {"grid": {"visible": true, "frameLocked": true, "label": "Grid", "instanceId": "e0ecd918-9fa0-4b25-be04-7fb1c6ba1c2e", "layerId": "foxglove.Grid", "size": 10, "divisions": 10, "lineWidth": 1, "color": "#248eff", "position": [0, 0, 0], "rotation": [0, 0, 0]}}, "publish": {"type": "point", "poseTopic": "/move_base_simple/goal", "pointTopic": "/clicked_point", "poseEstimateTopic": "/initialpose", "poseEstimateXDeviation": 0.5, "poseEstimateYDeviation": 0.5, "poseEstimateThetaDeviation": 0.26179939}, "imageMode": {}}, "RawMessages!221pe8g": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/uwb/data.angle_filtered", "fontSize": 30}, "RawMessages!1o9pyu7": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/uwb/processed.angle_filtered", "fontSize": 30}, "Image!wxqg09": {"cameraState": {"distance": 20, "perspective": true, "phi": 60, "target": [0, 0, 0], "targetOffset": [0, 0, 0], "targetOrientation": [0, 0, 0, 1], "thetaOffset": 45, "fovy": 45, "near": 0.5, "far": 5000}, "followMode": "follow-pose", "scene": {}, "transforms": {"frame:default_cam": {"visible": false}, "frame:": {"visible": false}, "frame:uwb": {"visible": false}, "frame:odom": {"visible": false}}, "topics": {"/uwb/processed/poses": {"visible": true, "axisScale": 0.5}, "/uwb/poses": {"visible": true, "axisScale": 0.2}, "/lidar_points": {"visible": true, "colorField": "intensity", "colorMode": "colormap", "colorMap": "turbo"}}, "layers": {}, "publish": {"type": "point", "poseTopic": "/move_base_simple/goal", "pointTopic": "/clicked_point", "poseEstimateTopic": "/initialpose", "poseEstimateXDeviation": 0.5, "poseEstimateYDeviation": 0.5, "poseEstimateThetaDeviation": 0.26179939}, "imageMode": {"imageTopic": "/image_left_raw/h264", "calibrationTopic": "/image_left_raw/camera_info"}}, "RawMessages!1r7n255": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/image_left_raw/h264", "fontSize": 12}, "RawMessages!240c24t": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/image_left_raw/camera_info", "fontSize": 12}, "Plot!3gj4h0n": {"paths": [{"timestampMethod": "receiveTime", "value": "/uwb/poses.poses[:].position.y", "enabled": true, "color": "#4e98e2", "xValuePath": "/uwb/poses.poses[:].position.x", "label": "origin"}, {"timestampMethod": "receiveTime", "value": "/uwb/processed/poses.poses[:].position.y", "enabled": true, "color": "#f5774d", "xValuePath": "/uwb/processed/poses.poses[:].position.x", "label": "filteredWithOdom"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "custom", "sidebarDimension": 240, "axisScalesMode": "lockedScales"}, "RawMessages!3vha4jx": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/uwb/data", "fontSize": 12}, "RawMessages!13tiihy": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/uwb/processed", "fontSize": 12}, "Tab!1aecszo": {"activeTabIdx": 2, "tabs": [{"title": "View", "layout": {"first": "DogLegSideView.DogLegSideView!4cpw19t", "second": {"first": "Plot!wd01tt", "second": "Plot!31we15j", "direction": "column"}, "direction": "row", "splitPercentage": 62.2820548280374}}, {"title": "<PERSON><PERSON><PERSON>", "layout": {"first": "RawMessages!1ybnn1v", "second": "NodePlayground!ewybt2", "direction": "row", "splitPercentage": 22.45353871684946}}, {"title": "Sensors", "layout": {"first": {"first": "3D!1sm58o5", "second": {"first": "RawMessages!221pe8g", "second": "RawMessages!1o9pyu7", "direction": "column"}, "direction": "row", "splitPercentage": 67.12282164028679}, "second": {"first": "Image!wxqg09", "second": {"first": "RawMessages!1r7n255", "second": "RawMessages!240c24t", "direction": "column"}, "direction": "row", "splitPercentage": 67.11594310819868}, "direction": "column"}}, {"title": "UWB", "layout": {"first": "Plot!3gj4h0n", "second": {"first": "RawMessages!3vha4jx", "second": "RawMessages!13tiihy", "direction": "row"}, "direction": "column", "splitPercentage": 60.47819971870605}}]}}, "globalVariables": {}, "userNodes": {"f4f5cbf3-1c58-4e03-b2f0-4d85075e8215": {"sourceCode": "import { Input } from \"./types\";\nimport { PosesInFrame, Pose } from \"@foxglove/schemas\"; // 按你的 foxglove sdk 自动引入即可\n\nexport const inputs = [\"/uwb/data\"];\nexport const output = \"/uwb/poses\";\n\nfunction toRadians(deg: number): number {\n  return (deg * Math.PI) / 180;\n}\n\nexport default function script(event: Input<\"/uwb/data\">): PosesInFrame {\n  const { header, angle, distance, pitch } = event.message;\n  const angle_ = toRadians(-1 * angle);\n  const pitchRad = toRadians(pitch);\n  const d = distance;\n\n  // 右手系 VCS 坐标\n  const x = d * Math.cos(angle_);\n  const y = d * Math.sin(angle_);\n  const z = d * Math.sin(pitchRad);\n\n  const pose: Pose = {\n    position: { x, y, z },\n    orientation: { x: 0, y: 0, z: 0, w: 1 }, // 单位四元数，无方向\n  };\n\n  return {\n    timestamp: header.stamp,\n    frame_id: header.frame_id || \"uwb\",\n    poses: [pose],\n  };\n}\n", "name": "uwb_point"}, "b1967854-c766-4bc5-a3a0-ba807b3c24c2": {"sourceCode": "import { Input } from \"./types\";\nimport { PosesInFrame, Pose } from \"@foxglove/schemas\"; // 按你的 foxglove sdk 自动引入即可\n\nexport const inputs = [\"/uwb/processed\"];\nexport const output = \"/uwb/processed/poses\";\n\nfunction toRadians(deg: number): number {\n  return (deg * Math.PI) / 180;\n}\n\nexport default function script(event: Input<\"/uwb/processed\">): PosesInFrame {\n  const { header, angle_filtered, distance_filtered, pitch } = event.message;\n  const angle = toRadians(-1 * angle_filtered);\n  const pitchRad = toRadians(pitch);\n  const d = distance_filtered;\n\n  // 右手系 VCS 坐标\n  const x = d * Math.cos(angle);\n  const y = d * Math.sin(angle);\n  const z = d * Math.sin(pitchRad);\n\n  const pose: Pose = {\n    position: { x, y, z },\n    orientation: { x: 0, y: 0, z: 0, w: 1 }, // 单位四元数，无方向\n  };\n\n  return {\n    timestamp: header.stamp,\n    frame_id: header.frame_id || \"uwb\",\n    poses: [pose],\n  };\n}\n", "name": "uwb_postprocess_point"}, "4830bd1e-2a97-4acd-a3ed-44ac045694cb": {"sourceCode": "import { Input } from \"./types\";\nimport { FrameTransforms, FrameTransform, Quaternion } from \"@foxglove/schemas\";\n\nexport const inputs = [\"/rt/odom\"];\nexport const output = \"/tf\";\n\nfunction rotmatToQuat(R: number[][]): Quaternion {\n  // 旋转矩阵转四元数（假设R是3x3数组）\n  const m00 = R[0][0],\n    m01 = R[0][1],\n    m02 = R[0][2];\n  const m10 = R[1][0],\n    m11 = R[1][1],\n    m12 = R[1][2];\n  const m20 = R[2][0],\n    m21 = R[2][1],\n    m22 = R[2][2];\n  const trace = m00 + m11 + m22;\n  let qw, qx, qy, qz;\n  if (trace > 0) {\n    let s = 0.5 / Math.sqrt(trace + 1.0);\n    qw = 0.25 / s;\n    qx = (m21 - m12) * s;\n    qy = (m02 - m20) * s;\n    qz = (m10 - m01) * s;\n  } else {\n    if (m00 > m11 && m00 > m22) {\n      let s = 2.0 * Math.sqrt(1.0 + m00 - m11 - m22);\n      qw = (m21 - m12) / s;\n      qx = 0.25 * s;\n      qy = (m01 + m10) / s;\n      qz = (m02 + m20) / s;\n    } else if (m11 > m22) {\n      let s = 2.0 * Math.sqrt(1.0 + m11 - m00 - m22);\n      qw = (m02 - m20) / s;\n      qx = (m01 + m10) / s;\n      qy = 0.25 * s;\n      qz = (m12 + m21) / s;\n    } else {\n      let s = 2.0 * Math.sqrt(1.0 + m22 - m00 - m11);\n      qw = (m10 - m01) / s;\n      qx = (m02 + m20) / s;\n      qy = (m12 + m21) / s;\n      qz = 0.25 * s;\n    }\n  }\n  return { x: qx, y: qy, z: qz, w: qw };\n}\n\nexport default function script(event: Input<\"/rt/odom\">): FrameTransforms {\n  const odom = event.message;\n\n  // 摄像头外参\n  const R = [\n    [-0.0130459, 0.999647, 0.0231345],\n    [0.065793, 0.0239446, -0.997545],\n    [-0.997748, -0.0114917, -0.0660821],\n  ];\n  const t = [0.0351892, 0.00365625, -0.259888];\n\n  // 1. odom -> uwb\n  const tf_odom_uwb: FrameTransform = {\n    timestamp: odom.header.stamp,\n    parent_frame_id: odom.header.frame_id || \"odom\",\n    child_frame_id: \"uwb\",\n    translation: {\n      x: odom.pose.pose.position.x,\n      y: odom.pose.pose.position.y,\n      z: odom.pose.pose.position.z,\n    },\n    rotation: {\n      x: odom.pose.pose.orientation.x,\n      y: odom.pose.pose.orientation.y,\n      z: odom.pose.pose.orientation.z,\n      w: odom.pose.pose.orientation.w,\n    },\n  };\n\n  // 2. uwb -> 图像坐标系（\"\"）\n  const quat = rotmatToQuat(R);\n  const tf_uwb_cam: FrameTransform = {\n    timestamp: odom.header.stamp,\n    parent_frame_id: \"\",\n    child_frame_id: \"hesai_lidar\",\n    translation: {\n      x: t[0],\n      y: t[1],\n      z: t[2],\n    },\n    rotation: quat,\n  };\n\n  const tf_uwb_lidar: FrameTransform = {\n    timestamp: odom.header.stamp,\n    parent_frame_id: \"hesai_lidar\",\n    child_frame_id: \"uwb\",\n    translation: {\n      x: 0,\n      y: 0,\n      z: 0,\n    },\n    rotation: {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1,\n    },\n  };\n\n  return {\n    transforms: [tf_odom_uwb, tf_uwb_cam, tf_uwb_lidar],\n  };\n}\n", "name": "tf"}}, "playbackConfig": {"speed": 1}, "layout": "Tab!1aecszo"}