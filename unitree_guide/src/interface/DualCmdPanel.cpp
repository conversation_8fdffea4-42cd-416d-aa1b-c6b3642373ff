#include "interface/DualCmdPanel.h"

DualCmdPanel::DualCmdPanel() {
  keyboard_.reset(new KeyBoard());
  joystick_.reset(new JoyStick());
}

DualCmdPanel::~DualCmdPanel() {}

UserCommand DualCmdPanel::getUserCmd() {
  UserCommand joyCmd = joystick_->getUserCmd();
  UserCommand keyCmd = keyboard_->getUserCmd();

  keyboard_->Reset();
  joystick_->Reset();

  if (joyCmd != UserCommand::NONE) {
    return joyCmd;
  }
  if (keyCmd != UserCommand::NONE) {
    return keyCmd;
  }
  return UserCommand::NONE;
}

UserValue DualCmdPanel::getUserValue() {
  UserValue joyValue = joystick_->getUserValue();
  UserValue keyValue = keyboard_->getUserValue();

  keyboard_->Reset();
  joystick_->Reset();

  if (joyValue.lx == 0 && joyValue.ly == 0 && joyValue.rx == 0 &&
      joyValue.ry == 0 && joyValue.L2 == 0) {
    return keyValue;
  }
  return joyValue;
}
