<?xml version="1.0"?>
<package format="2">
    <name>unitree_guide</name>
    <version>0.0.0</version>
    <description>The unitree_guide is an open source project for controlling the quadruped robot of Unitree Robotics, and it is also the software project accompanying 《四足机器人控制算法--建模、控制与实践》 published by Unitree Robotics.</description>

    <maintainer email="<EMAIL>">unitree</maintainer>
    <license>TODO</license>

    <buildtool_depend>catkin</buildtool_depend>
    <buildtool_depend>genmsg</buildtool_depend>
    <build_depend>controller_manager</build_depend>
    <build_depend>joint_state_controller</build_depend>
    <build_depend>robot_state_publisher</build_depend>
    <build_depend>roscpp</build_depend>
    <build_depend>std_msgs</build_depend>
    <build_export_depend>controller_manager</build_export_depend>
    <build_export_depend>joint_state_controller</build_export_depend>
    <build_export_depend>robot_state_publisher</build_export_depend>
    <build_export_depend>roscpp</build_export_depend>
    <build_export_depend>std_msgs</build_export_depend>
    <exec_depend>controller_manager</exec_depend>
    <exec_depend>joint_state_controller</exec_depend>
    <exec_depend>robot_state_publisher</exec_depend>
    <exec_depend>roscpp</exec_depend>
    <exec_depend>std_msgs</exec_depend>
    <depend>unitree_legged_msgs</depend>

    <!-- The export tag contains other, unspecified, tags -->
    <export>
        <!-- Other tools can request additional information be placed here -->
        <gazebo_ros plugin_path="${prefix}/lib" gazebo_media_path="${prefix}"/>
    </export>
</package>
