#!/bin/bash

# 速度控制脚本 - 用于安全地控制机器人速度命令
# 使用方法: ./velocity_control.sh [start|stop|status] [velocity] [yaw] [duration]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 默认参数
DEFAULT_VELOCITY=0.5
DEFAULT_YAW=0.0
DEFAULT_DURATION=5.0

# PID 文件
ARBITRATOR_PID_FILE="/tmp/command_arbitrator.pid"
VELOCITY_CMD_PID_FILE="/tmp/velocity_commander.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

function print_usage() {
    echo "用法: $0 [命令] [参数...]"
    echo ""
    echo "命令:"
    echo "  start [velocity] [yaw] [duration]  - 开始发送速度命令"
    echo "  stop                               - 停止所有速度命令"
    echo "  status                             - 查看当前状态"
    echo "  setup                              - 启动仲裁器节点"
    echo ""
    echo "参数:"
    echo "  velocity  - 线速度 (m/s), 默认: $DEFAULT_VELOCITY"
    echo "  yaw       - 角速度 (rad/s), 默认: $DEFAULT_YAW"
    echo "  duration  - 持续时间 (秒), 默认: $DEFAULT_DURATION"
    echo ""
    echo "示例:"
    echo "  $0 setup                    # 启动仲裁器"
    echo "  $0 start 0.3 0.0 10        # 前进0.3m/s，持续10秒"
    echo "  $0 start 0.0 1.0 5         # 原地旋转1.0rad/s，持续5秒"
    echo "  $0 stop                     # 停止所有命令"
    echo "  $0 status                   # 查看状态"
}

function check_ros_env() {
    if [ -z "$ROS_DISTRO" ]; then
        echo -e "${RED}错误: ROS环境未设置${NC}"
        echo "请先运行: source install/setup.bash"
        exit 1
    fi
}

function check_arbitrator() {
    if [ -f "$ARBITRATOR_PID_FILE" ]; then
        local pid=$(cat "$ARBITRATOR_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 仲裁器正在运行
        else
            rm -f "$ARBITRATOR_PID_FILE"
        fi
    fi
    return 1  # 仲裁器未运行
}

function start_arbitrator() {
    if check_arbitrator; then
        echo -e "${YELLOW}仲裁器已在运行${NC}"
        return 0
    fi
    
    echo -e "${GREEN}启动命令仲裁器...${NC}"
    
    # 启动仲裁器节点
    nohup bash -c "source $PROJECT_DIR/install/setup.bash && ros2 run him_pkg command_arbitrator" > /tmp/arbitrator.log 2>&1 &
    local pid=$!
    echo $pid > "$ARBITRATOR_PID_FILE"
    
    # 等待节点启动
    sleep 2
    
    if ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${GREEN}仲裁器启动成功 (PID: $pid)${NC}"
        return 0
    else
        echo -e "${RED}仲裁器启动失败${NC}"
        rm -f "$ARBITRATOR_PID_FILE"
        return 1
    fi
}

function stop_velocity_command() {
    if [ -f "$VELOCITY_CMD_PID_FILE" ]; then
        local pid=$(cat "$VELOCITY_CMD_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${YELLOW}停止速度命令 (PID: $pid)${NC}"
            kill -TERM "$pid" 2>/dev/null
            sleep 1
            if ps -p "$pid" > /dev/null 2>&1; then
                kill -KILL "$pid" 2>/dev/null
            fi
        fi
        rm -f "$VELOCITY_CMD_PID_FILE"
    fi
}

function stop_arbitrator() {
    if [ -f "$ARBITRATOR_PID_FILE" ]; then
        local pid=$(cat "$ARBITRATOR_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${YELLOW}停止仲裁器 (PID: $pid)${NC}"
            kill -TERM "$pid" 2>/dev/null
            sleep 1
            if ps -p "$pid" > /dev/null 2>&1; then
                kill -KILL "$pid" 2>/dev/null
            fi
        fi
        rm -f "$ARBITRATOR_PID_FILE"
    fi
}

function start_velocity_command() {
    local velocity=${1:-$DEFAULT_VELOCITY}
    local yaw=${2:-$DEFAULT_YAW}
    local duration=${3:-$DEFAULT_DURATION}
    
    # 检查仲裁器是否运行
    if ! check_arbitrator; then
        echo -e "${RED}错误: 仲裁器未运行，请先运行: $0 setup${NC}"
        exit 1
    fi
    
    # 停止现有的速度命令
    stop_velocity_command
    
    echo -e "${GREEN}开始发送速度命令:${NC}"
    echo "  线速度: ${velocity} m/s"
    echo "  角速度: ${yaw} rad/s"
    echo "  持续时间: ${duration} 秒"
    
    # 启动速度命令节点
    nohup bash -c "source $PROJECT_DIR/install/setup.bash && python3 $PROJECT_DIR/src/unittest/velocity_commander.py --velocity $velocity --yaw $yaw --duration $duration" > /tmp/velocity_cmd.log 2>&1 &
    local pid=$!
    echo $pid > "$VELOCITY_CMD_PID_FILE"
    
    echo -e "${GREEN}速度命令已启动 (PID: $pid)${NC}"
    echo "使用 '$0 stop' 来提前停止命令"
}

function show_status() {
    echo -e "${GREEN}=== 速度控制系统状态 ===${NC}"
    
    if check_arbitrator; then
        local pid=$(cat "$ARBITRATOR_PID_FILE")
        echo -e "仲裁器: ${GREEN}运行中${NC} (PID: $pid)"
    else
        echo -e "仲裁器: ${RED}未运行${NC}"
    fi
    
    if [ -f "$VELOCITY_CMD_PID_FILE" ]; then
        local pid=$(cat "$VELOCITY_CMD_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "速度命令: ${GREEN}运行中${NC} (PID: $pid)"
        else
            echo -e "速度命令: ${RED}未运行${NC}"
            rm -f "$VELOCITY_CMD_PID_FILE"
        fi
    else
        echo -e "速度命令: ${RED}未运行${NC}"
    fi
    
    echo ""
    echo "ROS话题状态:"
    echo "  /joy_cmd     - Joy控制命令"
    echo "  /script_cmd  - 脚本控制命令"
    echo "  /vel_cmd     - 最终输出命令"
}

# 主程序
case "${1:-help}" in
    "setup")
        check_ros_env
        start_arbitrator
        ;;
    "start")
        check_ros_env
        start_velocity_command "$2" "$3" "$4"
        ;;
    "stop")
        stop_velocity_command
        echo -e "${GREEN}速度命令已停止${NC}"
        ;;
    "stop-all")
        stop_velocity_command
        stop_arbitrator
        echo -e "${GREEN}所有服务已停止${NC}"
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        print_usage
        ;;
esac
