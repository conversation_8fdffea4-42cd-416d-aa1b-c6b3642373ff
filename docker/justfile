default:
  @just --list

build-from-zero http_proxy:
  HTTP_PROXY={{http_proxy}} docker compose run --rm build

build:
  docker compose run --rm build

rebuild:
  docker compose run --rm --build build

setup:
  ./setup_env.sh

run vnc_port-590X bridge_port:
  docker compose run --rm -p {{vnc_port-590X}}:5901 -p {{bridge_port}}:8765 run

rerun vnc_port-590X bridge_port:
  docker compose run --rm -p {{vnc_port-590X}}:5901 -p {{bridge_port}}:8765 --build run

debug-build:
  docker compose run --rm build_debug

arm:
  docker compose run --rm run_arm64
