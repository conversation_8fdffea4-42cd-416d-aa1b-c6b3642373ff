#!/bin/bash

# 设置VNC密码
if [ ! -f ~/.vnc/passwd ]; then
  mkdir -p ~/.vnc
  echo "${VNC_PASSWORD:-Vita@123}" | vncpasswd -f > ~/.vnc/passwd
  chmod 600 ~/.vnc/passwd
fi

# 使用VNC_DISPLAY变量设置显示号
DISPLAY_NUM=${VNC_DISPLAY:-1}

# 启动VNC服务器
vncserver :${DISPLAY_NUM} -geometry ${VNC_GEOMETRY:-3024x1964} -idletimeout 0

# 显示VNC连接信息
echo "VNC服务器已启动，连接端口: $((5900 + DISPLAY_NUM))"
echo "使用密码: ${VNC_PASSWORD:-Vita@123}"
echo "在 macOS 上使用命令: open vnc://root@${HOST_IP:-HOST_IP}:$((5900 + DISPLAY_NUM))"
