# 仿真环境

## 目标

- 使用 docker compose 来管理我们的仿真环境

## 使用方法

### 设置主机IP

在使用 Docker 环境进行开发或运行前，特别是需要通过 VNC 进行远程访问时，请先运行以下命令设置主机 IP 环境变量：

```bash
./setup_env.sh
```

该脚本会自动获取主机 IP 并将其写入项目根目录的 `.env` 文件，这样在使用 VNC 连接容器时可以正确显示连接信息。设置完成后，再执行其他 docker compose 命令。

### 一键构建（非交互式）

如果您只需要在容器中构建项目，并将构建产物保存在宿主机上，可以使用以下命令，因为要拉第三方库，所以需要挂一下代理：

```bash
cd docker
HTTP_PROXY=http://your-proxy:port docker compose run --rm build
```

这个命令会：
- 启动容器并自动执行 `build.sh` 脚本
- 构建完成后自动退出容器
- 构建产物会保存在宿主机的项目目录中

### 一次性使用（启动后自动销毁）

如果您只需临时使用容器并在退出后自动销毁（适合调试问题），可以使用以下命令：

```bash
cd docker
docker compose run --rm build_debug
```

这个命令会：
- 启动容器并直接进入交互式终端
- 当您从终端退出（使用 `exit` 命令）后，容器会自动销毁
- `--rm` 参数确保容器在使用后被完全删除

### 运行编译后的产物以及跑带 himloco 模型的闭环仿真（使用VNC远程访问）

我们使用 TurboVNC 和 VirtualGL 来提供高性能的 3D 图形渲染支持，特别适合在远程服务器上运行 MuJoCo 等图形密集型应用。

```bash
cd docker
docker compose run --rm -p 5901:5901 -p 8765:8765 run
```

这个命令会：
- 自动启动 TurboVNC 服务器，并通过 VirtualGL 启用 GPU 加速
- 容器内的 VNC 服务将在端口 5901 上运行
- 容器内的 foxglove_bridge 服务将在端口 8765 上运行

#### VNC连接

1. 在本地机器上安装VNC客户端（如TurboVNC Viewer、RealVNC Viewer或TigerVNC Viewer）
2. 连接到服务器的VNC会话：
   - 地址: `服务器IP:5901` (例如: `*************:5901`)
   - 密码: `Vita@123` (默认密码)
3. 连接后，您将看到一个完整的Xfce桌面环境，可以在其中运行图形应用程序
4. 在 macOS 上使用命令: `open vnc://root@服务器IP:5901`

#### 运行闭环仿真

按照上述流程在 VNC 桌面里打开 mujoco 窗口，然后在启动起来的终端里继续执行:

```bash
make himloco
```

即可看到 mujoco 里狗子站起来。

如果需要启动 virtual joystick 工具，可以在 VNC 桌面里在开一个终端，然后 `make joystick` 即可。
如果想使用 App 作为手柄，可以在容器内任意终端执行一下 `ros2 run foxglove_bridge foxglove_bridge` 即可。

#### 自定义VNC设置

您可以通过环境变量自定义VNC设置：

```bash
# 自定义VNC密码和用户名
VNC_USER=myuser VNC_PASSWORD=mysecretpass docker compose run --rm -p 5901:5901 -p 8765:8765 run

# 自定义VNC分辨率和DPI
VNC_RESOLUTION=1920x1080 docker compose run --rm -p 5901:5901 -p 8765:8765 run

# 自定义VNC端口（如果5901已被占用）
docker compose run --rm -p 6001:5901 -p 6002:8765 run
```

#### 在VNC中运行MuJoCo应用

```bash
cd /vitamin
make sim
```

即可看到仿真环境。

### 强制重新构建镜像

如果您修改了 Dockerfile.build 或 Dockerfile.run，需要强制重新构建镜像，增加 `--build` 参数：

```bash
# 方法1：使用 build 命令
docker compose build

# 方法2：run 时加上 --build 参数
docker compose run --rm --build build_debug
# 或者对于非交互式构建
docker compose run --rm --build build
# 或者对于运行时容器
docker compose run --rm --build run

# 方法3：up 时加上 --build 参数
docker compose up -d --build
```

### 代理设置

容器会自动设置 HTTP 代理，您可以通过环境变量自定义代理地址：

```bash
# 使用自定义代理地址
cd docker
HTTP_PROXY=http://your-proxy:port docker compose up -d

# 或使用一次性模式
HTTP_PROXY=http://your-proxy:port docker compose run --rm build_debug
# 非交互式构建
HTTP_PROXY=http://your-proxy:port docker compose run --rm build
# 运行编译产物
docker compose run --rm -p 5901:5901 run
```

如果不需要代理，可以传入空值：

```bash
HTTP_PROXY="" docker compose up -d
```

### Isaac Gym 训练环境

```sh
docker compose run --rm -p 590x:5901 train
```

在 macos 上 `open vnc://root@192.168.31.134:590x`

然后 vnc 界面里

```sh
conda activate py38 && export LD_LIBRARY_PATH=/opt/miniconda3/envs/py38/lib:$LD_LIBRARY_PATH && cd /opt/isaacgym/python/examples
python 1080_balls_of_solitude.py
```

应该可以看到 Issac Gym 的画面

# 数据挂载

参考 <https://vitadynamics.feishu.cn/wiki/JIckw7WtBiMornkf1vecCHPlngf>

在服务器上需要自行在自己的用户目录下挂载 minio 的 bucket，然后映射到容器内使用。

```sh
echo "LVHpuemT9Z7YjhTHdMDY:x4oj9x5hqgCGIafD4uejkl4VTeKdxvO2h026tmff" >> ~/.passwd-s3fs
chmod 600 ~/.passwd-s3fs
mkdir ~/minio-mount
mkdir ~/minio-mount/vita-data
mkdir ~/minio-mount/vita-algo-data
mkdir ~/minio-mount/vita-probe
s3fs vita-data ~/minio-mount/vita-data -o allow_other -o use_cache=/tmp/s3fs_cache -o use_path_request_style  -o passwd_file=/home/<USER>/.passwd-s3fs -o url=http://192.168.31.199:9000
s3fs vita-algo-data ~/minio-mount/vita-algo-data -o allow_other -o use_cache=/tmp/s3fs_cache -o use_path_request_style  -o passwd_file=/home/<USER>/.passwd-s3fs -o url=http://192.168.31.199:9000
s3fs vita-probe ~/minio-mount/vita-probe -o allow_other -o use_cache=/tmp/s3fs_cache -o use_path_request_style  -o passwd_file=/home/<USER>/.passwd-s3fs -o url=http://192.168.31.199:9000
```

