#FROM ros:humble
FROM **************:8000/library/ros:humble_amd64_v1

# 设置非交互、时区和语言
ENV DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    TZ=Asia/Shanghai

# 复制清华源配置文件
COPY sources.list /etc/apt/sources.list

# 安装桌面环境和基础依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        xfce4 xfce4-goodies xterm \
        libegl1-mesa mesa-utils \
        libgl1-mesa-glx libglu1-mesa \
        libxcb-keysyms1 libx11-6 libxrandr2 \
        libxcb1 libxtst6 libxi6 locales wget \
        supervisor curl ca-certificates \
        xauth x11-xkb-utils xkb-data libxv1 \
        x11-xserver-utils dbus-x11 \
        ros-humble-foxglove-bridge && \
    rm -rf /var/lib/apt/lists/*

# 设置版本常量
ENV VIRTUALGL_VERSION=3.1 \
    TURBOVNC_VERSION=3.1

# 下载并安装TurboVNC和VirtualGL
RUN cd /tmp && \
    wget -q "http://**************:8082/deps/turbovnc_${TURBOVNC_VERSION}_amd64.deb" && \
    wget -q "http://**************:8082/deps/virtualgl_${VIRTUALGL_VERSION}_amd64.deb" && \
    dpkg -i turbovnc_${TURBOVNC_VERSION}_amd64.deb && \
    dpkg -i virtualgl_${VIRTUALGL_VERSION}_amd64.deb && \
    rm -f turbovnc_${TURBOVNC_VERSION}_amd64.deb virtualgl_${VIRTUALGL_VERSION}_amd64.deb

# 设置miniconda路径
ENV PATH="/opt/miniconda3/bin:${PATH}"
ENV CONDA_AUTO_UPDATE_CONDA=false

# 下载miniconda并安装
RUN cd /tmp && \
    wget -q "http://**************:8082/deps/Miniconda3-py310_24.11.1-0-Linux-x86_64.sh" && \
    chmod +x Miniconda3-py310_24.11.1-0-Linux-x86_64.sh && \
    bash Miniconda3-py310_24.11.1-0-Linux-x86_64.sh -b -p /opt/miniconda3 && \
    rm -f Miniconda3-py310_24.11.1-0-Linux-x86_64.sh && \
    ln -s /opt/miniconda3/etc/profile.d/conda.sh /etc/profile.d/conda.sh && \
    echo ". /opt/miniconda3/etc/profile.d/conda.sh" >> ~/.bashrc

# 在conda中安装python3.10和torch1.13.0
RUN . /opt/miniconda3/etc/profile.d/conda.sh && \
    conda create -n himloco python=3.10 -y && \
    conda activate himloco && \
    conda install -c conda-forge libstdcxx-ng -y && \
    cd /tmp && \
    wget -q "http://**************:8082/deps/pillow-11.2.1-cp310-cp310-manylinux_2_28_x86_64.whl" && \
    wget -q "http://**************:8082/deps/nvidia_cublas_cu11-**********-py3-none-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/nvidia_cuda_nvrtc_cu11-11.7.99-2-py3-none-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/nvidia_cuda_runtime_cu11-11.7.99-py3-none-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/nvidia_cudnn_cu11-********-2-py3-none-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/torchaudio-0.13.0-cp310-cp310-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/torchvision-0.14.0-cp310-cp310-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/torch-1.13.0-cp310-cp310-manylinux1_x86_64.whl" && \
    wget -q "http://**************:8082/deps/typing_extensions-4.13.2-py3-none-any.whl" && \
    pip3 install /tmp/pillow-11.2.1-cp310-cp310-manylinux_2_28_x86_64.whl \
                 /tmp/nvidia_cublas_cu11-**********-py3-none-manylinux1_x86_64.whl \
                 /tmp/nvidia_cuda_nvrtc_cu11-11.7.99-2-py3-none-manylinux1_x86_64.whl \
                 /tmp/nvidia_cuda_runtime_cu11-11.7.99-py3-none-manylinux1_x86_64.whl \
                 /tmp/nvidia_cudnn_cu11-********-2-py3-none-manylinux1_x86_64.whl \
                 /tmp/torchaudio-0.13.0-cp310-cp310-manylinux1_x86_64.whl \
                 /tmp/torchvision-0.14.0-cp310-cp310-manylinux1_x86_64.whl \
                 /tmp/torch-1.13.0-cp310-cp310-manylinux1_x86_64.whl \
                 /tmp/typing_extensions-4.13.2-py3-none-any.whl \
                 lark \
                 scipy \
                 empy==3.3.4 \
                 onnxruntime \
                 "numpy<2.0.0" \
                 pybind11 \
                 pygame \
                 -i https://pypi.tuna.tsinghua.edu.cn/simple && \
    rm -f /tmp/*.whl

# 添加TurboVNC和VirtualGL到环境变量
ENV PATH="/opt/TurboVNC/bin:/opt/VirtualGL/bin:${PATH}"
ENV PYTHONPATH="/opt/miniconda3/envs/himloco/lib/python3.10/site-packages/:${PYTHONPATH}"

# 配置VNC启动脚本
RUN echo '#!/bin/sh' > /opt/TurboVNC/bin/xstartup.turbovnc && \
    echo 'xrdb $HOME/.Xresources' >> /opt/TurboVNC/bin/xstartup.turbovnc && \
    echo 'exec startxfce4' >> /opt/TurboVNC/bin/xstartup.turbovnc && \
    chmod +x /opt/TurboVNC/bin/xstartup.turbovnc

# 为VirtualGL正确配置X服务器
RUN mkdir -p /etc/VirtualGL && \
    echo "export VGL_DISPLAY=:1" >> /etc/bash.bashrc && \
    echo "export VGL_READBACK=pbo" >> /etc/bash.bashrc && \
    echo "export VGL_FORCEALPHA=1" >> /etc/bash.bashrc && \
    echo 'export VGL_DISPLAY=:${VNC_DISPLAY:-1}' >> /etc/bash.bashrc && \
    echo 'export DISPLAY=:${VNC_DISPLAY:-1}' >> /etc/bash.bashrc && \
    echo 'export __GL_SYNC_TO_VBLANK=0' >> /etc/bash.bashrc && \
    echo 'export __GLX_VENDOR_LIBRARY_NAME=nvidia' >> /etc/bash.bashrc

# 额外工具安装
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        ros-humble-rosbag2-storage-mcap \
        vim && \
    rm -rf /var/lib/apt/lists/* && \
    wget -q "http://**************:8082/deps/mcap-linux-amd64" -O /usr/local/bin/mcap && \
    chmod +x /usr/local/bin/mcap

# 复制启动脚本
COPY start.sh /start.sh
RUN chmod +x /start.sh

# VNC端口（根据VNC_DISPLAY设置）
EXPOSE 5901

# 设置启动命令，先运行start.sh，然后启动bash
CMD ["/bin/bash", "-c", "/start.sh && cd /vitamin && exec /bin/bash"]
