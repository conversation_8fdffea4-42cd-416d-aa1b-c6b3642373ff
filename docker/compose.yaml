services:
    # 交互式开发环境（保留用于调试）
    build_debug:
        build:
            context: .
            dockerfile: Dockerfile.build
            args:
                - HTTP_PROXY=${HTTP_PROXY:-}
        container_name: vitamin_container
        volumes:
            - ..:/vitamin
        working_dir: /vitamin
        environment:
            - ROS_DISTRO=humble
            - DEBIAN_FRONTEND=noninteractive
            - HTTP_PROXY=${HTTP_PROXY:-}
        command: /bin/bash
        tty: true
        stdin_open: true

    # 非交互式构建任务
    build:
        build:
            context: .
            dockerfile: Dockerfile.build
            args:
                - HTTP_PROXY=${HTTP_PROXY:-}
        volumes:
            - ..:/vitamin
        working_dir: /vitamin
        environment:
            - ROS_DISTRO=humble
            - DEBIAN_FRONTEND=noninteractive
            - HTTP_PROXY=${HTTP_PROXY:-}
        command: bash -c "make build"

    # 运行编译产物的容器（使用TurboVNC和VirtualGL进行高效渲染）
    run:
        build:
            context: .
            dockerfile: Dockerfile.run
        container_name: vitamin_run_container
        volumes:
            - ..:/vitamin
            - ~/minio-mount/vita-data:/vita-data
            - ~/minio-mount/vita-algo-data:/vita-algo-data
            - ~/minio-mount/vita-probe:/vita-probe
        environment:
            - ROS_DISTRO=humble
            - DEBIAN_FRONTEND=noninteractive
            - USE_VIRTUALGL=true
            # 设置 VNC 连接时的密码
            - VNC_PASSWORD=${VNC_PASSWORD:-Vita@123}
            # 设置 VNC 连接时的分辨率
            - VNC_GEOMETRY=${VNC_RESOLUTION:-1280x800}
            # 设置显示号 (从 1 开始)
            - VNC_DISPLAY=${VNC_DISPLAY:-1}
            - HOST_IP=${HOST_IP:-HOST_IP}
            # 设置Python路径
            - PYTHONPATH=/opt/miniconda3/envs/himloco/lib/python3.10/site-packages${PYTHONPATH:+:${PYTHONPATH}}
            - NVIDIA_VISIBLE_DEVICES=all
            - NVIDIA_DRIVER_CAPABILITIES=all
            - PULSE_SERVER=unix:/tmp/pulse-socket
        working_dir: /vitamin
        ports:
            - "590${VNC_DISPLAY:-1}:590${VNC_DISPLAY:-1}"
            - 8765:8765
        # 使用GPU
        deploy:
            resources:
                reservations:
                    devices:
                        - driver: nvidia
                          count: 1
                          capabilities:
                              [gpu, utility, compute, graphics, display]
        runtime: nvidia
        shm_size: 2gb
        tty: true
        stdin_open: true

    # 运行Arm64容器
    run_arm64:
        build:
            context: .
            dockerfile: Dockerfile.run_arm64
        # image: **************:8000/algo/docker-run_arm64:latest
        container_name: vitamin_run_arm64_container
        network_mode: host
        devices:
            - "/dev/input/js0:/dev/input/js0"
        volumes:
            - ..:/vitamin
            - /home/<USER>/home/<USER>
        environment:
            - ROS_DISTRO=humble
            - DEBIAN_FRONTEND=noninteractive
            - HOST_IP=${HOST_IP:-HOST_IP}
            # 设置Python路径
            - PYTHONPATH=/opt/miniconda3/envs/himloco/lib/python3.10/site-packages${PYTHONPATH:+:${PYTHONPATH}}
            - PULSE_SERVER=unix:/tmp/pulse-socket
            # 继承宿主机的环境变量
            # - FASTRTPS_DEFAULT_PROFILES_FILE=${FASTRTPS_DEFAULT_PROFILES_FILE}
            - CYCLONEDDS_URI=${CYCLONEDDS_URI}
            - ROS_DOMAIN_ID=${ROS_DOMAIN_ID}
            - RMW_IMPLEMENTATION=${RMW_IMPLEMENTATION}
            - LD_PRELOAD=/usr/lib/aarch64-linux-gnu/libstdc++.so.6
        working_dir: /vitamin
        shm_size: 2gb
        tty: true
        stdin_open: true

    # 新增训练服务（使用 Dockerfile.train）
    train:
        build:
            context: .
            dockerfile: Dockerfile.train
        container_name: vitamin_train_container
        environment:
            - DEBIAN_FRONTEND=noninteractive
            - HTTP_PROXY=${HTTP_PROXY:-}
            - NVIDIA_VISIBLE_DEVICES=all
            - NVIDIA_DRIVER_CAPABILITIES=all
        # 不挂载外部文件（仅保留容器内工作目录）
        volumes: []
        # 使用GPU
        deploy:
            resources:
                reservations:
                    devices:
                        - driver: nvidia
                          count: 1
                          capabilities:
                              [gpu, utility, compute, graphics, display]
        runtime: nvidia
        shm_size: 2gb
        tty: true
        stdin_open: true
        command: /bin/bash
