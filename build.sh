#!/bin/bash

# 获取当前脚本名称
SCRIPT_NAME=$(basename "$0")

# 创建 log 目录
LOG_DIR="log"
if [ ! -d $LOG_DIR ]; then
    mkdir -p $LOG_DIR
fi

# 生成带时间戳的日志文件名
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
RUNTIME_LOG="$LOG_DIR/runtime_$TIMESTAMP.log"
ERROR_LOG="$LOG_DIR/error_$TIMESTAMP.log"

# 日志函数，记录时间、脚本行数、日志等级和日志信息
log() {
  local log_level=$1
  local line_number=$2
  local message=$3
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  case $log_level in
    DEBUG)
      color="\033[36m" # 青色
      ;;
    WARNING)
      color="\033[33m" # 黄色
      ;;
    INFO)
      color="\033[32m" # 绿色
      ;;
    ERROR)
      color="\033[31m" # 红色
      ;;
    *)
      color="\033[0m" # 无色
      ;;
  esac
  reset_color="\033[0m"
  echo -e "${color}[$timestamp] [$SCRIPT_NAME:$line_number] [$log_level] $message${reset_color}"
}

# 为不同日志等级创建简化调用函数
debug_log() {
  log DEBUG "${BASH_LINENO[0]}" "$1"
}

info_log() {
  log INFO "${BASH_LINENO[0]}" "$1"
}

warning_log() {
  log WARNING "${BASH_LINENO[0]}" "$1"
}

error_log() {
  log ERROR "${BASH_LINENO[0]}" "$1"
}

# 默认编译模式
BUILD_TYPE="Release"

# 是否清除缓存标志
CLEAN_CACHE=false

# 指定的 DDS 实现
DDS_IMPLEMENTATION=""

# 是否启动项目标志
RUN_PROJECT=false

# 是否重新编译标志
RECOMPILE=false

# 打印帮助信息的函数
print_help() {
  echo "Usage: $0 [options]"
  echo "For example: bash build.sh -r"
  echo "Options:"
  echo "  -d, --debug                     Compile in debug mode."
  echo "  -c, --clean                     Clean the build cache."
  echo "  -D, --dds <dds_name>            Specify the DDS implementation (either fastrtps or cyclonedds)."
  echo "  -r, --run                       Complie and run the project. If 'install/setup.bash' exists, skip compilation."
  echo "  -R, --recompile                 Clean the cache, recompile the project, and then run it."
  echo "  -h, --help                      Print this help message"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case "$1" in
    -d|--debug)
      BUILD_TYPE="Debug"
      info_log "Compiling in debug mode"
      shift
      ;;
    -c|--clean)
      CLEAN_CACHE=true
      shift
      ;;
    -D|--dds)
      DDS_IMPLEMENTATION="$2"
      shift 2
      ;;
    -r|--run)
      RUN_PROJECT=true
      shift
      ;;
    -R|--recompile)
      RECOMPILE=true
      CLEAN_CACHE=true
      RUN_PROJECT=true
      shift
      ;;
    -h|--help)
      print_help
      exit 0
      ;;
    *)
      error_log "Unknown option: $1"
      print_help
      exit 1
      ;;
  esac
done

# 清除编译缓存
if $CLEAN_CACHE; then
  info_log "Clearing build cache..."
  rm -rf build install log
fi

ROS_SETUP_PATH="/opt/ros/humble/setup.bash"
source $ROS_SETUP_PATH

# 判断是否需要编译
NEED_COMPILE=true
if $RUN_PROJECT && [ -f "install/setup.bash" ] && ! $RECOMPILE; then
  info_log "'install/setup.bash' exists. Skipping compilation."
  NEED_COMPILE=false
fi

# DDS 相关操作仅在需要编译时执行
if $NEED_COMPILE; then
  # 检查指定的 DDS 实现是否安装
  if [ -z "$DDS_IMPLEMENTATION" ]; then
    DDS_IMPLEMENTATION="fastrtps"
  fi

  PACKAGE_NAME="ros-$ROS_DISTRO-rmw-${DDS_IMPLEMENTATION}-cpp"
  if dpkg -s "$PACKAGE_NAME" >/dev/null 2>&1; then
    info_log "The DDS implementation $PACKAGE_NAME is already installed."
  else
    warning_log "The DDS implementation $PACKAGE_NAME is not installed. Starting installation..."
    sudo apt update
    sudo apt install -y "$PACKAGE_NAME"
  fi

  info_log "export RMW_IMPLEMENTATION=\"rmw_${DDS_IMPLEMENTATION}_cpp\""
  export RMW_IMPLEMENTATION="rmw_${DDS_IMPLEMENTATION}_cpp"
fi

# 编译整个项目
if $NEED_COMPILE; then
  info_log "Starting to compile vita_sim in $BUILD_TYPE mode..."
  colcon build --parallel-workers 16 --cmake-args -DCMAKE_BUILD_TYPE=$BUILD_TYPE

  # 输出编译结果
  if [ $? -eq 0 ]; then
    info_log "Compilation successful!"
  else
    error_log "Compilation failed. Please check the error messages."
    exit 1
  fi
fi

# 启动项目
if $RUN_PROJECT; then
  info_log "colcon build --parallel-workers 16 --cmake-args -DCMAKE_BUILD_TYPE=$BUILD_TYPE"
  colcon build --parallel-workers 16 --cmake-args -DCMAKE_BUILD_TYPE=$BUILD_TYPE
  # 输出编译结果
  if [ $? -eq 0 ]; then
    info_log "Compilation successful! Run vita_sim\n runtime log:\t $RUNTIME_LOG\n error log:\t $ERROR_LOG"
  else
    error_log "Compilation failed. Please check the error messages."
    exit 1
  fi


  info_log "Sourcing 'install/setup.bash'..."
  source install/setup.bash
  info_log "ros2 run vita_sim vita_mujoco"
  ros2 run vita_sim vita_mujoco >> $RUNTIME_LOG 2>> $ERROR_LOG
fi
