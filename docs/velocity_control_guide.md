# 速度控制系统使用指南

## 概述

本系统实现了一个安全的速度控制机制，允许脚本临时接管机器人的速度控制，同时确保与外部 Joy 节点不产生冲突。

## 系统架构

```
Joy节点 (/joy) -> JoyNode -> /joy_cmd
                                    \
                                     CommandArbitrator -> /vel_cmd -> 机器人控制系统
                                    /
脚本命令 -> VelocityCommander -> /script_cmd
```

### 核心组件

1. **CommandArbitrator (仲裁器)**
   - 订阅 `/joy_cmd` 和 `/script_cmd`
   - 发布到 `/vel_cmd`
   - 实现优先级控制和超时机制

2. **VelocityCommander (速度命令器)**
   - 发布指定的速度命令到 `/script_cmd`
   - 支持持续时间控制
   - 自动停止和清理

3. **JoyNode (Joy节点)**
   - 处理手柄输入
   - 发布到 `/joy_cmd`

## 工作原理

### 优先级机制
- **默认状态**: 仲裁器转发 Joy 命令 (`/joy_cmd` -> `/vel_cmd`)
- **脚本激活**: 当收到脚本命令时，立即切换到脚本控制
- **超时恢复**: 脚本命令超时(0.5秒)后，自动切换回 Joy 控制

### 安全机制
- 脚本命令超时后发送零速度命令
- 脚本结束时发送零速度命令
- 支持 Ctrl+C 中断

## 使用方法

### 1. 准备工作

首先确保系统已编译并设置环境：

```bash
# 编译项目
make build

# 设置ROS环境
source install/setup.bash
```

### 2. 启动仲裁器

```bash
# 方法1: 使用便捷脚本
./scripts/velocity_control.sh setup

# 方法2: 使用make命令
make arbitrator

# 方法3: 直接运行
ros2 run him_pkg command_arbitrator
```

### 3. 发送速度命令

```bash
# 使用便捷脚本 (推荐)
./scripts/velocity_control.sh start [velocity] [yaw] [duration]

# 示例
./scripts/velocity_control.sh start 0.3 0.0 10    # 前进0.3m/s，10秒
./scripts/velocity_control.sh start 0.0 1.0 5     # 原地旋转1.0rad/s，5秒

# 使用make命令
make velocity_command vel=0.5 dur=5.0

# 直接运行
python3 src/unittest/velocity_commander.py --velocity 0.5 --yaw 0.0 --duration 5.0
```

### 4. 监控和控制

```bash
# 查看状态
./scripts/velocity_control.sh status

# 停止速度命令
./scripts/velocity_control.sh stop

# 停止所有服务
./scripts/velocity_control.sh stop-all
```

## 完整使用流程

### 场景1: 临时脚本控制

```bash
# 1. 启动仲裁器
./scripts/velocity_control.sh setup

# 2. 启动Joy节点 (如果需要)
make joystick

# 3. 发送脚本命令
./scripts/velocity_control.sh start 0.3 0.0 10

# 此时机器人会执行脚本命令，Joy输入被忽略
# 10秒后自动恢复Joy控制
```

### 场景2: 紧急停止

```bash
# 在任何时候都可以停止脚本命令
./scripts/velocity_control.sh stop

# 或者使用 Ctrl+C 中断正在运行的命令
```

## 话题说明

| 话题名 | 类型 | 说明 |
|--------|------|------|
| `/joy` | sensor_msgs/Joy | 原始手柄输入 |
| `/joy_cmd` | geometry_msgs/Twist | Joy节点处理后的速度命令 |
| `/script_cmd` | geometry_msgs/Twist | 脚本发送的速度命令 |
| `/vel_cmd` | geometry_msgs/Twist | 仲裁器输出的最终速度命令 |

## 参数配置

### VelocityCommander 参数
- `--velocity`: 线速度 (m/s)，默认 0.5
- `--yaw`: 角速度 (rad/s)，默认 0.0
- `--duration`: 持续时间 (秒)，默认 5.0

### CommandArbitrator 参数
- 超时时间: 0.5秒 (硬编码)
- 检查频率: 10Hz (硬编码)

## 故障排除

### 1. "No executable found" 错误

```bash
# 重新编译him_pkg包
colcon build --packages-select him_pkg

# 或使用Docker构建
cd docker && docker compose run --rm build
```

### 2. 权限错误

```bash
# 检查脚本权限
chmod +x scripts/velocity_control.sh

# 检查Python脚本权限
chmod +x src/unittest/velocity_commander.py
```

### 3. 仲裁器无响应

```bash
# 检查仲裁器状态
./scripts/velocity_control.sh status

# 重启仲裁器
./scripts/velocity_control.sh stop-all
./scripts/velocity_control.sh setup
```

### 4. 话题连接问题

```bash
# 检查话题
ros2 topic list | grep -E "(joy|vel|script)_cmd"

# 监控话题
ros2 topic echo /vel_cmd
```

## 安全注意事项

1. **测试环境**: 首先在仿真环境中测试
2. **速度限制**: 使用合理的速度值，避免过大的加速度
3. **紧急停止**: 确保能够快速停止命令
4. **监控**: 始终监控机器人状态
5. **超时设置**: 0.5秒超时确保快速恢复控制

## 扩展功能

### 自定义脚本

可以创建自己的速度控制脚本，只需发布到 `/script_cmd` 话题：

```python
#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist

class CustomVelocityController(Node):
    def __init__(self):
        super().__init__('custom_velocity_controller')
        self.publisher = self.create_publisher(Twist, '/script_cmd', 10)
        # 实现自定义逻辑...
```

### 修改超时时间

编辑 `src/ros2_him/src/him_pkg/him_pkg/utils/arbitrator_node.py`:

```python
self.timeout = 1.0  # 修改为1秒超时
```
